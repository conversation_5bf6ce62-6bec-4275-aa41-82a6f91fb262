# Copilot Instructions for Atomize Frontend

## Project Overview
Atomize is an educational platform with gamification features built on Next.js 14 with TypeScript. The platform includes role-based access (students, teachers, coordinators), real-time multiplayer battles, XP/Atom currency system, inventory management, and AI-powered chat features.

## Architecture Patterns

### App Router Structure
- **Role-based route groups**: `(browse)`, `(browse_teacher)`, `(browse_coordinator)`, `(browse_gestor)`, `(auth)`, `(interno)`
- **Feature-first organization**: Each route contains its own `_components`, `_constants`, `_utils` subdirectories
- **Shared layouts**: Role-specific layouts with different navigation and theming

### Service Architecture
```
services/
├── ASSESSMENT/     # Assessment and evaluation services
├── CALENDAR/       # Calendar and scheduling
├── CDS/           # Core Data Services (users, schools, groups)
├── GAMES/         # Real-time multiplayer battle system
├── MATRIXIS/      # Balance and metrics
├── PATHS/         # Learning paths and progression
├── QAAS/          # Questions as a Service
├── SAPS/          # Student activity and progress
├── SHOP/          # Virtual shop and inventory
└── axios.ts       # Global axios configuration with JWT interceptors
```

Each service follows the pattern: `api/`, `interfaces/`, `docs/` with TypeScript interfaces and API controllers.

### Context System
- **AuthContext**: JWT-based authentication with role-based routing in `middleware.ts`
- **UserContext**: User data and balance (XP/Atoms) management
- **ChallengeContext**: Complex WebSocket-based real-time battle system with game state management

## Key Development Patterns

### Gamification System
- **XP (Experience Points)**: Primary progression currency earned through activities
- **Atoms**: Secondary currency for shop purchases
- **League System**: Player ranking with tiers (Estelar, Nebulosa, Planetária)
- **Inventory**: Items with status tracking (`Ready`, `Redeemed`, `active`) and metadata

### Real-time Battle System
The battle system uses WebSocket connections through `services/GAMES/`:
- **Game States**: `'initialized' | 'inLobby' | 'playing' | 'finished'`
- **Player Management**: Lives, scores, XP changes tracked per player
- **Question Flow**: Sequential challenge levels with difficulty progression
- **Notifications**: In-game event system for player actions

### Component Conventions
- **UI Components**: Located in `components/ui/` following shadcn/ui patterns
- **Feature Components**: Co-located with routes using `_components/` directories
- **Animations**: Extensive use of Framer Motion for transitions and micro-interactions
- **Theming**: CSS variables with Tailwind, forced dark theme in root layout

### API Integration Patterns
- **Service Controllers**: Each service has dedicated controller classes (e.g., `UserController`, `InventoryController`)
- **Interface Definitions**: Strong TypeScript typing for all API responses
- **Error Handling**: Axios interceptors handle token refresh automatically
- **Token Management**: JWT stored in both localStorage and cookies for SSR compatibility

## Development Workflows

### Pre-Development Setup
```bash
npm run predev  # Cleans temp-uploads directory before starting
npm run dev     # Start development server
```

### Environment Variables Required
- `NEXT_PUBLIC_ATOMIZE_CDS_URL`: Backend API base URL
- `NEXT_PUBLIC_ATOMIZE_API_KEY`: API authentication key
- `OPEN_AI_API_KEY`: For AI chat agents (math, physics, chemistry, portuguese tutors)

### Testing Battle System
1. Navigate to `/batalha` (battles)
2. Create/join rooms with XP requirements
3. Test WebSocket connections through `ChallengeContext`
4. Monitor real-time state updates and notifications

### Working with Inventory System
- Items have complex metadata and status workflows
- Redemption process: `Ready` → `Redeemed` with generated codes/URLs
- Use `InventoryController.moveInventoryItem()` for status changes
- Handle optimistic updates in UI components

## Critical File Dependencies

### Authentication Flow
- `middleware.ts`: Route protection and role-based redirects
- `context/AuthContext.tsx`: JWT management and user state
- `services/authentication/authService.ts`: Login/logout API calls

### Battle System Core
- `context/ChallengeContext.tsx`: WebSocket state management
- `services/GAMES/`: All battle-related interfaces and services
- `app/(browse)/batalha/`: Battle UI components and flows

### Styling System
- `app/globals.css`: CSS variables and base styles
- `tailwind.config.ts`: Extended theme with custom colors and animations
- `lib/utils.ts`: `cn()` function for conditional class merging

## Common Pitfalls
- **XP Validation**: Always check user XP before allowing battle participation
- **WebSocket Cleanup**: Properly disconnect WebSocket listeners in useEffect cleanup
- **Token Expiry**: Middleware handles expired tokens, but check localStorage validity
- **Role-based Access**: Use `authData?.role` checks for feature gating
- **Status Updates**: Inventory item status changes require both local state and API updates

## AI Integration
The platform includes specialized AI tutors accessible via `/api/chat/`:
- **Professor Pitágoras**: Mathematics tutor with LaTeX formula support
- **Doutora Einstein**: Physics concepts with real-world analogies
- **Mestre Lavoisier**: Chemistry explanations with lab context
- **Professora Clarice**: Portuguese language and literature

Use `$$formula$$` for block math and `$formula$` for inline math in AI responses.
