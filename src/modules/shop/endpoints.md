# Shop Module Endpoints Documentation

This document provides details about all endpoints available in the Shop module, including their paths, methods, request parameters, and response formats.

## Shop Endpoints

### Create Shop

Creates a new shop with optional contract ID and initial items.

- **Endpoint:** `/shop`
- **Method:** `POST`
- **Authentication:** Required (Admin role)
- **Request Body:**
  ```json
  {
    "contractId": "contract123", // Optional - unique if provided
    "itemIds": ["id"]
  }
  ```

- **Response:**
  ```json
  {
    "id": "shop123",
    "contractId": "contract123",
    "items": [
      {
        "id": "item123",
        "name": "Item Name",
        "description": "Item description",
        "currentPrice": 90,
        "discount": 0.1,
        "stock": 50,
        "images": [],
        "category": "clothing",
        "type": "AtomizeMerch"
      }
    ]
  }
  ```

### Add Items to Shop

Adds items to an existing shop.

- **Endpoint:** `/shop/:shopId/items`
- **Method:** `POST`
- **Authentication:** Required (Admin role)
- **Path Parameters:**
  - `shopId`: ID of the shop to add items to
- **Request Body:**
  ```json
  {
    "items": [
      {
        "item": {
          "id": "item456",
          "name": "New Item",
          "description": "New item description",
          "basePrice": 150,
          "currentPrice": 140,
          "discount": 0.07,
          "stock": 25,
          "category": "accessories",
          "type": "AtomizeMerch",
          "usageType": "Retrievable"
        },
        "order": 1
      }
    ]
  }
  ```

- **Response:**
  ```json
  {
    "id": "shop123",
    "contractId": "contract123",
    "items": [
      {
        "id": "item123",
        "name": "Item Name",
        "description": "Item description",
        "currentPrice": 90,
        "discount": 0.1,
        "stock": 50,
        "images": [],
        "category": "clothing",
        "type": "AtomizeMerch"
      },
      {
        "id": "item456",
        "name": "New Item",
        "description": "New item description",
        "currentPrice": 140,
        "discount": 0.07,
        "stock": 25,
        "images": [],
        "category": "accessories",
        "type": "AtomizeMerch"
      }
    ]
  }
  ```

### Remove Items from Shop

Removes items from an existing shop.

- **Endpoint:** `/shop/:shopId/items`
- **Method:** `DELETE`
- **Authentication:** Required (Admin role)
- **Path Parameters:**
  - `shopId`: ID of the shop to remove items from
- **Request Body:**
  ```json
  {
    "itemIds": ["item123", "item456"]
  }
  ```

- **Response:**
  ```json
  {
    "id": "shop123",
    "contractId": "contract123",
    "items": []
  }
  ```

### Get Shop Items

Retrieves shop items from both global and local shops based on user's contract.

- **Endpoint:** `/shop/shops`
- **Method:** `GET`
- **Authentication:** Required
- **Query Parameters:**
  - `userContractId` (optional): User's contract ID to get local shop items

- **Example Request:**
  ```
  GET /shop/items?userContractId=contract123
  ```

- **Response:**
  ```json
  {
    "GLOBAL": [
      {
        "id": "item123",
        "name": "Global Item",
        "description": "Available to all users",
        "currentPrice": 100,
        "discount": 0,
        "stock": 50,
        "images": ["https://example.com/image.jpg"],
        "category": "clothing",
        "type": "AtomizeMerch"
      }
    ],
    "LOCAL": [
      {
        "id": "item456",
        "name": "Local Item",
        "description": "Available to contract users only",
        "currentPrice": 150,
        "discount": 0.1,
        "stock": 25,
        "images": ["https://example.com/image2.jpg"],
        "category": "accessories",
        "type": "AtomizeMerch"
      }
    ]
  }
  ```

## Items Endpoints

### Get Items By Filter

Retrieves shop items based on specified filters.

- **Endpoint:** `/shop/items`
- **Method:** `GET`
- **Authentication:** Required
- **Query Parameters:**
  - `ids` (optional): Comma-separated list of item IDs
  - `search` (optional): Search term for item names/descriptions
  - `category` (optional): Filter by category
  - `type` (optional): Filter by item type
  - `usageType` (optional): Filter by usage type
  - `tags` (optional): Comma-separated list of tags
  - `minCurrentPrice` (optional): Minimum price filter
  - `maxCurrentPrice` (optional): Maximum price filter
  - `inStock` (optional): Boolean to filter items in stock ('true'/'false')
  - `limit` (optional): Number of items per page (default: 10)
  - `page` (optional): Page number (default: 1)

- **Example Request:**
  ```
  GET /shop/items?category=clothing&limit=20&page=1
  ```

- **Response:**
  ```json
  {
    "items": [
      {
        "id": "item123",
        "name": "Item Name",
        "description": "Item description",
        "category": "clothing",
        "type": "accessory",
        "usageType": "avatar",
        "tags": ["rare", "limited"],
        "currentPrice": 100,
        "originalPrice": 120,
        "inStock": true,
        "stockQuantity": 50,
        "createdAt": "2023-05-01T12:00:00Z",
        "updatedAt": "2023-05-10T14:30:00Z"
      }
    ],
    "pagination": {
      "totalItems": 45,
      "totalPages": 3,
      "currentPage": 1,
      "limit": 20
    }
  }
  ```

### Create Items

Creates new shop items (admin only).

- **Endpoint:** `/shop/items`
- **Method:** `POST`
- **Authentication:** Required (Admin role)
- **Request Body:**
  ```json
  {
    "itemsData": [
      {
        "name": "New Item",
        "description": "Item description",
        "category": "clothing",
        "type": "accessory",
        "usageType": "avatar",
        "tags": ["rare", "limited"],
        "currentPrice": 100,
        "originalPrice": 120,
        "stockQuantity": 50
      }
    ],
    "skipExisting": false
  }
  ```

- **Response:**
  ```json
  {
    "created": [
      {
        "id": "item123",
        "name": "New Item",
        "description": "Item description",
        "category": "clothing",
        "type": "accessory",
        "usageType": "avatar",
        "tags": ["rare", "limited"],
        "currentPrice": 100,
        "originalPrice": 120,
        "inStock": true,
        "stockQuantity": 50,
        "createdAt": "2023-05-01T12:00:00Z",
        "updatedAt": "2023-05-01T12:00:00Z"
      }
    ],
    "skipped": []
  }
  ```

### Update Items

Updates existing shop items (admin only).

- **Endpoint:** `/shop/items/update`
- **Method:** `PATCH`
- **Authentication:** Required (Admin role)
- **Request Body:**
  ```json
  {
    "itemsData": [
      {
        "id": "item123",
        "currentPrice": 90,
        "stockQuantity": 45
      }
    ]
  }
  ```

- **Response:**
  ```json
  {
    "updated": [
      {
        "id": "item123",
        "name": "Item Name",
        "currentPrice": 90,
        "stockQuantity": 45,
        "updatedAt": "2023-05-15T10:20:00Z"
      }
    ],
    "failed": []
  }
  ```

### Get Items Resume

Retrieves a summary of shop items statistics.

- **Endpoint:** `/shop/items/resume`
- **Method:** `GET`
- **Authentication:** Required (Admin role)

- **Response:**
  ```json
  {
    "totalItems": 150,
    "categoryCounts": {
      "clothing": 45,
      "accessories": 35,
      "emotes": 70
    },
    "typeCounts": {
      "common": 80,
      "rare": 50,
      "legendary": 20
    },
    "outOfStockCount": 15,
    "totalValue": 15000
  }
  ```

## Inventory Endpoints

### Get Inventory Items

Retrieves a user's inventory items based on specified filters.

- **Endpoint:** `/shop/inventory`
- **Method:** `GET`
- **Authentication:** Required
- **Query Parameters:**
  - `status` (optional): Filter by item status
  - `itemType` (optional): Filter by item type
  - `usageType` (optional): Filter by usage type
  - `tags` (optional): Comma-separated list of tags

- **Example Request:**
  ```
  GET /shop/inventory?itemType=clothing&tags=rare,limited
  ```

- **Response:**
  ```json
  {
    "items": [
      {
        "id": "inv123",
        "itemId": "item123",
        "userId": "user456",
        "name": "Item Name",
        "description": "Item description",
        "category": "clothing",
        "type": "accessory",
        "usageType": "avatar",
        "tags": ["rare", "limited"],
        "acquiredAt": "2023-05-15T10:20:00Z",
        "status": "active"
      }
    ],
    "totalItems": 5
  }
  ```

### Create Empty Inventory

Creates an empty inventory for a user.

- **Endpoint:** `/shop/inventory`
- **Method:** `POST`
- **Authentication:** Required
- **Request Body:**
  ```json
  {
    "contractId": "contract123" // Optional
  }
  ```

- **Response:**
  ```json
  {
    "id": "inv123",
    "userId": "user456",
    "contractId": "contract123", // If provided
    "items": [],
    "createdAt": "2023-05-15T10:20:00Z"
  }
  ```

### Give Item to Inventory

Adds an item to a user's inventory.

- **Endpoint:** `/shop/inventory/add-item`
- **Method:** `POST`
- **Authentication:** Required
- **Request Body:**
  ```json
  {
    "userId": "user456", // Required for admin, omitted for regular users
    "itemId": "item123",
    "quantity": 1
  }
  ```

- **Response:**
  ```json
  {
    "success": true,
    "inventoryItem": {
      "id": "invItem123",
      "itemId": "item123",
      "userId": "user456",
      "name": "Item Name",
      "description": "Item description",
      "category": "clothing",
      "type": "accessory",
      "usageType": "avatar",
      "tags": ["rare", "limited"],
      "acquiredAt": "2023-05-15T10:20:00Z",
      "status": "active"
    }
  }
  ```

### Add Bought Item from Shop

Adds a bought item from the shop to the user's inventory.

- **Endpoint:** `/shop/inventory/buy-item`
- **Method:** `POST`
- **Authentication:** Required
- **Request Body:**
  ```json
  {
    "inventoryId": "inv123",
    "shopItem": {
      "item": {
        "id": "item123",
        "name": "Item Name",
        "description": "Item description",
        "basePrice": 100,
        "currentPrice": 90,
        "discount": 0.1,
        "stock": 50,
        "category": "clothing",
        "type": "AtomizeMerch",
        "usageType": "Retrievable"
      }
    },
    "quantity": 1,
    "buyedPrice": 90,
    "itemMetadata": {
      "buyId": "purchase-123",
      "purchaseDate": "2023-05-15T10:20:00Z"
    }
  }
  ```

- **Response:**
  ```json
  {
    "id": "inv123",
    "ownerId": "user456",
    "contractId": null,
    "items": [
      {
        "itemInfo": {
          "id": "item123",
          "name": "Item Name",
          "description": "Item description",
          "currentPrice": 90,
          "tags": [],
          "category": "clothing",
          "type": "AtomizeMerch",
          "images": [],
          "usageType": "Retrievable"
        },
        "itemId": "item123",
        "quantity": 1,
        "status": "PaymentProcessing",
        "buyedPrice": 90,
        "itemMetadata": {
          "buyId": "purchase-123",
          "purchaseDate": "2023-05-15T10:20:00Z"
        },
        "order": 0
      }
    ],
    "statistics": {
      "numberOfItems": 1,
      "totalValue": 90,
      "paidValue": 90
    }
  }
  ```

### Make Inventory Trade

Facilitates a trade between two users' inventories.

- **Endpoint:** `/shop/inventory/trade`
- **Method:** `POST`
- **Authentication:** Required
- **Request Body:**
  ```json
  {
    "sourceUserId": "user456",
    "targetUserId": "user789",
    "items": [
      {
        "inventoryItemId": "invItem123",
        "quantity": 1
      }
    ]
  }
  ```

- **Response:**
  ```json
  {
    "success": true,
    "transactionId": "trans123",
    "items": [
      {
        "id": "invItem123",
        "itemId": "item123",
        "previousOwner": "user456",
        "newOwner": "user789",
        "transferredAt": "2023-05-15T10:20:00Z"
      }
    ]
  }
  ```

### Move Inventory Item

Moves an item within or between a user's inventory sections (e.g., active to storage).

- **Endpoint:** `/shop/inventory/move-item`
- **Method:** `POST`
- **Authentication:** Required
- **Request Body:**
  ```json
  {
    "itemId": "item123",
    "targetStatus": "Ready", // Optional - if not provided, will auto-determine next state
    "metadata": {}, // Optional - additional metadata for the movement
    "order": 0 // Optional - specific order if multiple items with same itemId
  }
  ```

- **Response:**
  ```json
  {
    "success": true,
    "inventory": {
      "id": "inv123",
      "ownerId": "user456",
      "items": [
        {
          "itemId": "item123",
          "status": "Ready",
          "itemMetadata": {
            "movementHistory": [
              {
                "movedAt": "2023-05-15T10:20:00Z",
                "previousStatus": "Pending",
                "newStatus": "Ready",
                "movementType": "manual"
              }
            ]
          }
        }
      ]
    },
    "movement": {
      "itemId": "item123",
      "itemName": "Item Name",
      "previousStatus": "Pending",
      "newStatus": "Ready",
      "movementType": "manual",
      "movedAt": "2023-05-15T10:20:00Z"
    }
  }
  ```

### Admin Move Inventory Item

Admin-only endpoint to move an item within any user's inventory sections.

- **Endpoint:** `/shop/inventory/admin/move-item`
- **Method:** `POST`
- **Authentication:** Required (Admin role)
- **Request Body:**
  ```json
  {
    "targetOwnerId": "user789", // Required - ID of the user whose inventory to modify
    "itemId": "item123",
    "targetStatus": "Ready", // Optional - if not provided, will auto-determine next state
    "metadata": {}, // Optional - additional metadata for the movement
    "order": 0 // Optional - specific order if multiple items with same itemId
  }
  ```

- **Response:**
  ```json
  {
    "success": true,
    "inventory": {
      "id": "inv789",
      "ownerId": "user789",
      "items": [
        {
          "itemId": "item123",
          "status": "Ready",
          "itemMetadata": {
            "movementHistory": [
              {
                "movedAt": "2023-05-15T10:20:00Z",
                "previousStatus": "Pending",
                "newStatus": "Ready",
                "movementType": "admin-manual",
                "adminUserId": "admin123"
              }
            ]
          }
        }
      ]
    },
    "movement": {
      "itemId": "item123",
      "itemName": "Item Name",
      "previousStatus": "Pending",
      "newStatus": "Ready",
      "movementType": "admin-manual",
      "movedAt": "2023-05-15T10:20:00Z",
      "adminUserId": "admin123",
      "targetOwnerId": "user789"
    }
  }
  ```

### Get Users Inventories Resume

Retrieves a summary of multiple users' inventories.

- **Endpoint:** `/shop/inventory/user-resume`
- **Method:** `GET`
- **Authentication:** Required (Admin role)
- **Query Parameters:**
  - `userIds` (optional): Comma-separated list of user IDs

- **Response:**
  ```json
  {
    "users": [
      {
        "userId": "user456",
        "totalItems": 15,
        "itemsByCategory": {
          "clothing": 5,
          "accessories": 10
        },
        "rareItems": 3,
        "legendaryItems": 1
      }
    ]
  }
  ```

### Get User Inventories Transactions Resume

Retrieves a summary of inventory transactions for a user.

- **Endpoint:** `/shop/inventory/transactions-resume`
- **Method:** `GET`
- **Authentication:** Required
- **Query Parameters:**
  - `startDate` (optional): Filter transactions after this date
  - `endDate` (optional): Filter transactions before this date

- **Response:**
  ```json
  {
    "transactions": [
      {
        "id": "trans123",
        "type": "purchase",
        "items": [
          {
            "id": "invItem123",
            "itemId": "item123",
            "name": "Item Name"
          }
        ],
        "date": "2023-05-15T10:20:00Z",
        "otherParty": "shop"
      },
      {
        "id": "trans124",
        "type": "trade",
        "items": [
          {
            "id": "invItem124",
            "itemId": "item124",
            "name": "Another Item"
          }
        ],
        "date": "2023-05-16T14:30:00Z",
        "otherParty": "user789"
      }
    ],
    "totalTransactions": 2
  }
  ```

### Get All Inventory Items with Status Filter (Admin Only)

Retrieves all inventory items across all users with status filtering. This endpoint is restricted to admin users only.

- **Endpoint:** `/shop/inventory/admin/items-with-status`
- **Method:** `GET`
- **Authentication:** Required (Admin role)
- **Query Parameters:**
  - `status` (required): Filter by item status (e.g., 'Pending', 'Ready', 'Active', 'PaymentProcessing')
  - `contractId` (optional): Filter by contract ID (use 'null' for global shop items)
  - `itemType` (optional): Filter by item type
  - `usageType` (optional): Filter by usage type
  - `tags` (optional): Comma-separated list of tags
  - `limit` (optional): Number of items per page (default: 50, max: 100)
  - `page` (optional): Page number (default: 1)

- **Example Request:**
  ```
  GET /shop/inventory/admin/items-with-status?status=Pending&contractId=contract123&limit=20&page=1
  ```

- **Response:**
  ```json
  {
    "items": [
      {
        "itemId": "item123",
        "quantity": 1,
        "status": "Pending",
        "buyedPrice": 90,
        "itemMetadata": {
          "buyId": "purchase-123",
          "purchaseDate": "2023-05-15T10:20:00Z"
        },
        "order": 0,
        "inventoryId": "inv123",
        "ownerId": "user456",
        "contractId": "contract123",
        "itemInfo": {
          "id": "item123",
          "name": "Item Name",
          "description": "Item description",
          "currentPrice": 90,
          "tags": [],
          "category": "clothing",
          "type": "AtomizeMerch",
          "images": [],
          "usageType": "Retrievable"
        }
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalItems": 75,
      "itemsPerPage": 20,
      "hasNextPage": true,
      "hasPrevPage": false
    },
    "filters": {
      "status": "Pending",
      "contractId": "contract123",
      "itemType": null,
      "usageType": null,
      "tags": null
    }
  }
  ```

## Error Responses

All endpoints may return the following error responses:

- **Validation Error (400):**
  ```json
  {
    "error": "ValidationError",
    "message": "Description of the validation error",
    "formattedMessage": "Human-readable error message"
  }
  ```

- **Authorization Error (403):**
  ```json
  {
    "error": "AuthorizationError",
    "message": "Insufficient permissions",
    "formattedMessage": "You don't have the required permissions to perform this action"
  }
  ```

- **Not Found Error (404):**
  ```json
  {
    "error": "NotFoundError",
    "message": "Resource not found",
    "formattedMessage": "The requested resource could not be found"
  }
  ```

- **Unexpected Error (500):**
  ```json
  {
    "error": "UnexpectedError",
    "message": "An error occurred",
    "formattedMessage": "An unexpected error occurred"
  }
  ``` 