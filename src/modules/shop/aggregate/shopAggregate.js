import { TypeValidator, EnumValidator, CompositeValidator } from '../../../utils/validators/index.js';
import { ValidationError } from '../../../utils/customErrors/index.js';
import { ItemAggregate } from './itemAggregate.js';

// ShopItem validator
const shopItemTypesValidator = new TypeValidator({
    item: 'object',
    order: 'number',
});

const shopItemValidator = new CompositeValidator(shopItemTypesValidator);

/**
 * Represents an item available in the shop, wrapping an ItemAggregate.
 * @typedef {Object} ShopItemData
 * @property {Object} item - The item data (raw or ItemAggregate)
 * @property {number} order - Order in the shop
 */
export class ShopItem {
    constructor({ item, order = 0 }) {
        if (!item) {
            throw new Error('Item cannot be null or undefined');
        }
        
        if (item instanceof ItemAggregate) {
            this.item = item;
        } else {
            this.item = ItemAggregate.fromData(item);
        }
        this.price = this.item.currentPrice;
        this.discount = this.item.discount;
        this.stock = this.item.stock;
        this.order = order;
        shopItemValidator.validate({ item: this.item, price: this.price, discount: this.discount, stock: this.stock, order });
    }

    static fromData(data) {
        return new ShopItem(data);
    }

    toData() {
        return {
            item: this.item.toData(),
            order: this.order,
        };
    }

    /**
     * Returns DTO for shop display: currentPrice, discount, stock, description, name, etc.
     */
    toDTO() {
        return {
            id: this.item.id,
            name: this.item.name,
            description: this.item.description,
            currentPrice: this.item.currentPrice,
            discount: this.item.discount,
            stock: this.item.stock,
            images: this.item.images,
            category: this.item.category,
            type: this.item.type,
        };
    }
}

// ShopAggregate validator
const shopTypesValidator = new TypeValidator({
    id: 'string',
    contractId: 'string', // not obligatory
    items: 'array',
});

const shopValidator = new CompositeValidator(shopTypesValidator);

/**
 * Aggregate for the shop, holding buyable items.
 * @typedef {Object} ShopAggregateData
 * @property {string} id - Shop id
 * @property {string} [contractId] - Optional contract id
 * @property {Array<ShopItem>} items - Items for sale
 */
export class ShopAggregate {
    constructor({ id, contractId = null, items = [] }) {
        this.id = id;
        this.contractId = contractId;
        // Filter out items with null item data before creating ShopItem instances
        this.items = items && Array.isArray(items) 
            ? items
                .filter(it => it && it.item) // Filter out null items or items with null item data
                .map(it => ShopItem.fromData(it)) 
            : [];
        shopValidator.validate({ id, contractId: contractId || '', items: this.items });
    }

    static fromData(data) {
        if (data._id && !data.id) data.id = data._id.toString();
        if (data.contractId && !(typeof data.contractId === 'string')) data.contractId = data.contractId.toString();
        return new ShopAggregate(data);
    }

    toData() {
        return {
            id: this.id,
            contractId: this.contractId,
            items: this.items.map(item => item.toData()),
        };
    }

    toDTO() {
        return {
            id: this.id,
            contractId: this.contractId,
            items: this.items.map(item => item.toDTO()),
        };
    }
} 