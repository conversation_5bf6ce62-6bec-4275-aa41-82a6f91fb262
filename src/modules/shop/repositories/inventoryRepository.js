import mongoose from 'mongoose';
import { InventoryAggregate, InventoryItem} from '../aggregate/inventoryAggregate.js';
import { Inventory, Item } from '../model/index.js';
import { NotFoundError, ValidationError } from '../../../utils/customErrors/index.js';
import ItemRepository from './itemRepository.js';

function parsePopulatedItems(items){
  return items.map(item => {
    return {...item, item: item.itemId, itemId: item.itemId._id};
  })
}

export class InventoryRepository {
  /**
   * Creates a new inventory for a user.
   * @param {string} ownerId - The ID of the inventory owner.
   * @param {string} [contractId] - Optional contract ID associated with this inventory.
   * @param {mongoose.ClientSession} [session] - Optional MongoDB session for transactions.
   * @returns {Promise<InventoryAggregate>} - The created inventory aggregate.
   */
  static async create({ ownerId, contractId, session }) {
    try {
      console.log('[REPOSITORY-CREATE] Starting create for ownerId:', ownerId);
      
      // Check if user already has an inventory
      const existingInventory = await Inventory.findOne({ ownerId });
      console.log('[REPOSITORY-CREATE] Existing inventory found:', !!existingInventory);
      
      if (existingInventory) {
        console.log('[REPOSITORY-CREATE] Returning existing inventory via findByOwnerId');
        return await InventoryRepository.findByOwnerId({ ownerId });
      }
      
      // Create new inventory
      const inventoryData = {
        ownerId,
        contractId,
        items: [],
        statistics: {
          numberOfItems: 0,
          totalValue: 0,
          paidValue: 0
        }
      };
      
      const inventory = session 
        ? await (new Inventory(inventoryData)).save({ session }) 
        : await (new Inventory(inventoryData)).save();
      
      return await InventoryRepository.findByOwnerId({ ownerId });
    } catch (error) {
      console.log('[REPOSITORY-CREATE-INVENTORY]', error);
      throw new Error(`Error creating inventory: ${error.message}`);
    }
  }

  /**
   * Finds an inventory by owner ID.
   * @param {string} ownerId - The ID of the owner.
   * @param {boolean} isPopulated - Whether to populate item details using mongoose populate.
   * @returns {Promise<InventoryAggregate>} - The found inventory aggregate.
   */
  static async findByOwnerId({ ownerId, isPopulated = false }) {
    try {
      let inventory;
      
      if (isPopulated) {
        // Use mongoose populate to get item details
        inventory = await Inventory.findOne({ ownerId }).populate('items.itemId');
        console.log('[INVENTORY-FIND-BY-OWNER-ID-POPULATED]', inventory);
      } else {
        // Get inventory without populating
        inventory = await Inventory.findOne({ ownerId });
        console.log('[INVENTORY-FIND-BY-OWNER-ID-NOT-POPULATED]', inventory);
      }
      
      if (!inventory) {
        // Create a new inventory if one doesn't exist
        console.log('[INVENTORY-NOT-FOUND] Creating new inventory for ownerId:', ownerId);
        return await InventoryRepository.create({ ownerId });
      }
      
      console.log('[INVENTORY-FOUND] Items count:', inventory.items.length);
      console.log('[INVENTORY-ITEMS-RAW]', inventory.items);
      
      let populatedItems;
      
      if (isPopulated && inventory.items.length > 0) {
        // Items are already populated by mongoose, just transform them
        populatedItems = inventory.items.map(inventoryItem => {
          const itemDetails = inventoryItem.itemId; // This is the populated item object
          
          return {
            item: itemDetails ? itemDetails.toObject() : null,
            itemId: itemDetails ? itemDetails._id : inventoryItem.itemId,
            quantity: inventoryItem.quantity,
            status: inventoryItem.status,
            buyedPrice: inventoryItem.buyedPrice,
            itemMetadata: inventoryItem.itemMetadata,
            order: inventoryItem.order
          };
        });
      } else {
        // Manual fetching for non-populated case or when no items exist
        const itemIds = inventory.items.map(item => item.itemId);
        const items = itemIds.length > 0 ? await Item.find({ _id: { $in: itemIds } }) : [];
        
        // Merge item details with inventory items
        populatedItems = inventory.items.map(inventoryItem => {
          const itemDetails = items.find(item => 
            item._id.toString() === inventoryItem.itemId.toString()
          );
          
          return {
            item: itemDetails ? itemDetails.toObject() : null,
            itemId: inventoryItem.itemId,
            quantity: inventoryItem.quantity,
            status: inventoryItem.status,
            buyedPrice: inventoryItem.buyedPrice,
            itemMetadata: inventoryItem.itemMetadata,
            order: inventoryItem.order
          };
        });
      }
      
      return InventoryAggregate.fromData({
        id: inventory._id,
        ownerId: inventory.ownerId,
        contractId: inventory.contractId,
        items: populatedItems,
        statistics: inventory.statistics
      });
    } catch (error) {
      console.log('[REPOSITORY-FIND-BY-OWNER-ID]', error);
      throw new Error(`Error finding inventory: ${error.message}`);
    }
  }

  /**
   * Finds an inventory by ID.
   * @param {string} inventoryId - The ID of the inventory.
   * @returns {Promise<InventoryAggregate>} - The found inventory aggregate.
   */
  static async findById({ inventoryId }) {
    try {
      console.log('[REPOSITORY-FIND-BY-ID] Looking for inventory with ID:', inventoryId);
      const inventory = await Inventory.findById(inventoryId);
      console.log('[REPOSITORY-FIND-BY-ID] Found inventory:', !!inventory);
      
      if (!inventory) {
        throw new NotFoundError(`Inventory with ID ${inventoryId} not found.`);
      }
      
      console.log('[REPOSITORY-FIND-BY-ID] Inventory items count:', inventory.items.length);
      console.log('[REPOSITORY-FIND-BY-ID] Raw inventory items:', inventory.items);
      
      // Get full item information for each inventory item
      const itemIds = inventory.items.map(item => item.itemId);
      console.log('[REPOSITORY-FIND-BY-ID] Item IDs to fetch:', itemIds);
      
      const items = await Item.find({ _id: { $in: itemIds } });
      console.log('[REPOSITORY-FIND-BY-ID] Found items:', items.length);
      
      // Merge item details with inventory items
      const populatedItems = inventory.items.map(inventoryItem => {
        const itemDetails = items.find(item => 
          item._id.toString() === inventoryItem.itemId.toString()
        );
        
        console.log(`[REPOSITORY-FIND-BY-ID] Processing item ${inventoryItem.itemId}, found details:`, !!itemDetails);
        
        return {
          item: itemDetails ? itemDetails.toObject() : null,
          itemId: inventoryItem.itemId,
          quantity: inventoryItem.quantity,
          status: inventoryItem.status,
          buyedPrice: inventoryItem.buyedPrice,
          itemMetadata: inventoryItem.itemMetadata
        };
      });
      
      console.log('[REPOSITORY-FIND-BY-ID] Populated items:', populatedItems.length);
      
      return InventoryAggregate.fromData({
        id: inventory._id,
        ownerId: inventory.ownerId,
        contractId: inventory.contractId,
        items: populatedItems,
        statistics: inventory.statistics
      });
    } catch (error) {
      console.log('[REPOSITORY-FIND-BY-ID]', error);
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new Error(`Error finding inventory: ${error.message}`);
    }
  }

  /**
   * Adds an item to an inventory.
   * @param {string} ownerId - The ID of the inventory owner.
   * @param {string} itemId - The ID of the item to add.
   * @param {number} quantity - The quantity of items to add.
   * @param {string} status - The status of the item in the inventory.
   * @param {number} buyedPrice - The price the item was purchased at.
   * @param {Object} itemMetadata - Additional metadata for the item.
   * @param {mongoose.ClientSession} [session] - Optional MongoDB session for transactions.
   * @returns {Promise<InventoryAggregate>} - The updated inventory aggregate.
   */
  static async addItem({ ownerId, itemId, quantity, status, buyedPrice, itemMetadata, session }) {
    try {
      // Get the inventory with session if provided
      let inventory = session 
        ? await Inventory.findOne({ ownerId }).session(session)
        : await Inventory.findOne({ ownerId });
      console.log('[INVENTORY-ADDITEM]', inventory);
      
      // Create a new inventory if one doesn't exist
      if (!inventory) {
        const inventoryData = {
          ownerId, 
          items: [],
          statistics: {
            numberOfItems: 0,
            totalValue: 0,
            paidValue: 0
          }
        };
        
        inventory = session 
          ? await (new Inventory(inventoryData)).save({ session })
          : await (new Inventory(inventoryData)).save();
        console.log('[INVENTORY-CREATED]', inventory);
      }
      
      // Get the item with session if provided
      const item = session 
        ? await Item.findById(itemId).session(session)
        : await Item.findById(itemId);
      console.log('[ITEM-ADDITEM]', item);
      
      if (!item) {
        throw new NotFoundError(`Item with ID ${itemId} not found.`);
      }
      
      // Check if item already exists in inventory
      // inventory.items.findIndex(
      //   invItem => invItem.itemId.toString() === itemId && JSON.stringify(invItem.itemMetadata) === JSON.stringify(itemMetadata)
      // );
      // removed for now.
      const existingItemIndex = -1;
      
      if (existingItemIndex !== -1) {
        // Update existing item quantity
        inventory.items[existingItemIndex].quantity += quantity;
        console.log('[INVENTORY-ITEM-UPDATED] Existing item quantity updated');
      } else {
        // Add new item to inventory
        inventory.items.push({
          itemId,
          quantity,
          status,
          buyedPrice: buyedPrice || item.currentPrice,
          itemMetadata: itemMetadata || {},
          order: inventory.items.length // Set order to current length for new items
        });
        console.log('[INVENTORY-ITEM-ADDED] New item added to inventory');
      }
      
      // Update inventory statistics
      inventory.statistics.numberOfItems = inventory.items.length;
      inventory.statistics.totalValue = inventory.items.reduce((total, invItem) => {
        return total + (invItem.quantity * (invItem.buyedPrice || 0));
      }, 0);
      inventory.statistics.paidValue = inventory.items.reduce((total, invItem) => {
        return total + (invItem.quantity * (invItem.buyedPrice || 0));
      }, 0);
      
      console.log('[INVENTORY-STATISTICS-UPDATED]', inventory.statistics);
      
      // Save the inventory with session if provided
      await (session ? inventory.save({ session }) : inventory.save());
      console.log('[INVENTORY-ADDITEM-SAVED]', inventory);

      // Get full item details for the aggregate - pass session if available
      return await InventoryRepository.findByOwnerId({ ownerId });
    } catch (error) {
      console.log('[REPOSITORY-ADDITEM]', error);
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new Error(`Error adding item to inventory: ${error.message}`);
    }
  }

  /**
   * Removes an item from an inventory.
   * @param {string} ownerId - The ID of the inventory owner.
   * @param {string} itemId - The ID of the item to remove.
   * @param {mongoose.ClientSession} [session] - Optional MongoDB session for transactions.
   * @returns {Promise<InventoryAggregate>} - The updated inventory aggregate.
   */
  static async removeItem({ ownerId, itemId, session }) {
    try {
      // Get the inventory
      const inventory = await Inventory.findOne({ ownerId });
      
      if (!inventory) {
        throw new NotFoundError(`Inventory for owner ${ownerId} not found.`);
      }
      
      // Find the item in the inventory
      const itemIndex = inventory.items.findIndex(
        invItem => invItem.itemId.toString() === itemId
      );
      
      if (itemIndex === -1) {
        throw new NotFoundError(`Item with ID ${itemId} not found in inventory.`);
      }
      
      // Remove the item
      inventory.items.splice(itemIndex, 1);
      
      // Update inventory statistics
      inventory.statistics.numberOfItems = inventory.items.length;
      inventory.statistics.totalValue = inventory.items.reduce((total, invItem) => {
        return total + (invItem.quantity * (invItem.buyedPrice || 0));
      }, 0);
      inventory.statistics.paidValue = inventory.items.reduce((total, invItem) => {
        return total + (invItem.quantity * (invItem.buyedPrice || 0));
      }, 0);
      
      // Save the inventory
      await (session ? inventory.save({ session }) : inventory.save());
      
      // Get full item details for the aggregate
      return InventoryRepository.findByOwnerId({ ownerId });
    } catch (error) {
      console.log('[REPOSITORY-REMOVE-ITEM]', error);
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new Error(`Error removing item from inventory: ${error.message}`);
    }
  }

  /**
   * Updates the quantity of an item in an inventory.
   * @param {string} ownerId - The ID of the inventory owner.
   * @param {string} itemId - The ID of the item to update.
   * @param {number} newQuantity - The new quantity for the item.
   * @param {mongoose.ClientSession} [session] - Optional MongoDB session for transactions.
   * @returns {Promise<InventoryAggregate>} - The updated inventory aggregate.
   */
  static async updateItemQuantity({ ownerId, itemId, newQuantity, session }) {
    try {
      // Get the inventory
      const inventory = await Inventory.findOne({ ownerId });
      
      if (!inventory) {
        throw new NotFoundError(`Inventory for owner ${ownerId} not found.`);
      }
      
      // Find the item in the inventory
      const itemIndex = inventory.items.findIndex(
        invItem => invItem.itemId.toString() === itemId
      );
      
      if (itemIndex === -1) {
        throw new NotFoundError(`Item with ID ${itemId} not found in inventory.`);
      }
      
      if (newQuantity < 0) {
        throw new ValidationError('Item quantity cannot be negative.');
      }
      
      if (newQuantity === 0) {
        // Remove the item if quantity is zero
        inventory.items.splice(itemIndex, 1);
      } else {
        // Update the quantity
        inventory.items[itemIndex].quantity = newQuantity;
      }
      
      // Update inventory statistics
      inventory.statistics.numberOfItems = inventory.items.length;
      inventory.statistics.totalValue = inventory.items.reduce((total, invItem) => {
        return total + (invItem.quantity * (invItem.buyedPrice || 0));
      }, 0);
      inventory.statistics.paidValue = inventory.items.reduce((total, invItem) => {
        return total + (invItem.quantity * (invItem.buyedPrice || 0));
      }, 0);
      
      // Save the inventory
      await (session ? inventory.save({ session }) : inventory.save());
      
      // Get full item details for the aggregate
      return InventoryRepository.findByOwnerId({ ownerId });
    } catch (error) {
      console.log('[REPOSITORY-UPDATE-ITEM-QUANTITY]', error);
      if (error instanceof NotFoundError || error instanceof ValidationError) {
        throw error;
      }
      throw new Error(`Error updating item quantity in inventory: ${error.message}`);
    }
  }

  /**
   * Updates the status of an item in an inventory.
   * @param {string} ownerId - The ID of the inventory owner.
   * @param {string} itemId - The ID of the item to update.
   * @param {string} newStatus - The new status for the item.
   * @param {mongoose.ClientSession} [session] - Optional MongoDB session for transactions.
   * @returns {Promise<InventoryAggregate>} - The updated inventory aggregate.
   */
  static async updateItemStatus({ ownerId, itemId, newStatus, session }) {
    try {
      // Get the inventory
      const inventory = await Inventory.findOne({ ownerId });
      
      if (!inventory) {
        throw new NotFoundError(`Inventory for owner ${ownerId} not found.`);
      }
      
      // Find the item in the inventory
      const itemIndex = inventory.items.findIndex(
        invItem => invItem.itemId.toString() === itemId
      );
      
      if (itemIndex === -1) {
        throw new NotFoundError(`Item with ID ${itemId} not found in inventory.`);
      }
      
      // Update the status
      inventory.items[itemIndex].status = newStatus;
      
      // Save the inventory
      await (session ? inventory.save({ session }) : inventory.save());
      
      // Get full item details for the aggregate
      return InventoryRepository.findByOwnerId({ ownerId });
    } catch (error) {
      console.log('[REPOSITORY-UPDATE-ITEM-STATUS]', error);
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new Error(`Error updating item status in inventory: ${error.message}`);
    }
  }

  /**
   * Transfers an item between inventories.
   * @param {string} sourceOwnerId - The ID of the source inventory owner.
   * @param {string} targetOwnerId - The ID of the target inventory owner.
   * @param {string} itemId - The ID of the item to transfer.
   * @param {number} quantity - The quantity to transfer.
   * @param {string} newStatus - The status for the item in the target inventory.
   * @param {mongoose.ClientSession} [session] - Optional MongoDB session for transactions.
   * @returns {Promise<{sourceInventory: InventoryAggregate, targetInventory: InventoryAggregate}>}
   */
  static async transferItem({ sourceOwnerId, targetOwnerId, itemId, quantity, newStatus, session }) {
    const sess = session || await mongoose.startSession();
    
    try {
      if (!session) {
        sess.startTransaction();
      }
      
      // Get both inventories
      const sourceInventory = await Inventory.findOne({ ownerId: sourceOwnerId }).session(sess);
      
      if (!sourceInventory) {
        throw new NotFoundError(`Source inventory for owner ${sourceOwnerId} not found.`);
      }
      
      let targetInventory = await Inventory.findOne({ ownerId: targetOwnerId }).session(sess);
      
      if (!targetInventory) {
        // Create target inventory if it doesn't exist
        targetInventory = new Inventory({
          ownerId: targetOwnerId,
          items: [],
          statistics: {
            numberOfItems: 0,
            totalValue: 0,
            paidValue: 0
          }
        });
      }
      
      // Find the item in the source inventory
      const sourceItemIndex = sourceInventory.items.findIndex(
        invItem => invItem.itemId.toString() === itemId
      );
      
      if (sourceItemIndex === -1) {
        throw new NotFoundError(`Item with ID ${itemId} not found in source inventory.`);
      }
      
      const sourceItem = sourceInventory.items[sourceItemIndex];
      
      if (sourceItem.quantity < quantity) {
        throw new ValidationError(`Not enough items to transfer. Available: ${sourceItem.quantity}, Requested: ${quantity}`);
      }
      
      // Get item details
      const item = await Item.findById(itemId).session(sess);
      if (!item) {
        throw new NotFoundError(`Item with ID ${itemId} not found.`);
      }
      
      // Update source inventory
      if (sourceItem.quantity === quantity) {
        // Remove item completely
        sourceInventory.items.splice(sourceItemIndex, 1);
      } else {
        // Reduce quantity
        sourceInventory.items[sourceItemIndex].quantity -= quantity;
      }
      
      // Update target inventory
      const targetItemIndex = targetInventory.items.findIndex(
        invItem => invItem.itemId.toString() === itemId
      );
      
      if (targetItemIndex !== -1) {
        // Increase quantity of existing item
        targetInventory.items[targetItemIndex].quantity += quantity;
        if (newStatus) {
          targetInventory.items[targetItemIndex].status = newStatus;
        }
      } else {
        // Add new item to target inventory
        targetInventory.items.push({
          itemId,
          quantity,
          status: newStatus || sourceItem.status,
          buyedPrice: sourceItem.buyedPrice,
          itemMetadata: sourceItem.itemMetadata,
          order: targetInventory.items.length // Set order to current length for new items
        });
      }
      
      // Update statistics for both inventories
      sourceInventory.statistics.numberOfItems = sourceInventory.items.length;
      sourceInventory.statistics.totalValue = sourceInventory.items.reduce((total, invItem) => {
        return total + (invItem.quantity * (invItem.buyedPrice || 0));
      }, 0);
      sourceInventory.statistics.paidValue = sourceInventory.items.reduce((total, invItem) => {
        return total + (invItem.quantity * (invItem.buyedPrice || 0));
      }, 0);
      
      targetInventory.statistics.numberOfItems = targetInventory.items.length;
      targetInventory.statistics.totalValue = targetInventory.items.reduce((total, invItem) => {
        return total + (invItem.quantity * (invItem.buyedPrice || 0));
      }, 0);
      targetInventory.statistics.paidValue = targetInventory.items.reduce((total, invItem) => {
        return total + (invItem.quantity * (invItem.buyedPrice || 0));
      }, 0);
      
      // Save both inventories
      await sourceInventory.save({ session: sess });
      await targetInventory.save({ session: sess });
      
      if (!session) {
        await sess.commitTransaction();
      }
      
      // Get full item details for the aggregates
      const sourceAgg = await InventoryRepository.findByOwnerId({ ownerId: sourceOwnerId });
      const targetAgg = await InventoryRepository.findByOwnerId({ ownerId: targetOwnerId });
      
      return {
        sourceInventory: sourceAgg,
        targetInventory: targetAgg
      };
    } catch (error) {
      if (!session) {
        await sess.abortTransaction();
      }
      
      console.log('[REPOSITORY-TRANSFER-ITEM]', error);
      if (error instanceof NotFoundError || error instanceof ValidationError) {
        throw error;
      }
      throw new Error(`Error transferring item between inventories: ${error.message}`);
    } finally {
      if (!session) {
        sess.endSession();
      }
    }
  }

  static async incrementItemQuantityInInventory({ inventoryId, inventorySubItemId, quantityToAdd, session }) {
    try {
      console.log(`[REPOSITORY-INCREMENT-ITEM-QUANTITY] Inventory ID: ${inventoryId}, SubItem ID: ${inventorySubItemId}, Quantity to Add: ${quantityToAdd}`);
      const updatedInventory = await Inventory.findOneAndUpdate(
        { _id: inventoryId, "items._id": inventorySubItemId },
        { $inc: { "items.$.quantity": quantityToAdd } },
        { new: true, session }
      );

      if (!updatedInventory) {
        throw new NotFoundError(`Could not find inventory with ID ${inventoryId} or sub-item with ID ${inventorySubItemId} to increment quantity.`);
      }
      
      console.log('[REPOSITORY-INCREMENT-ITEM-QUANTITY] Successfully incremented quantity.');
      // The use case expects the full InventoryAggregate, but for now, returning the raw doc
      // is simpler and the use case can re-fetch or be adapted if necessary.
      // For direct compatibility with how findById works, we should transform it here.
      // However, the calling use case currently handles updatedInventoryDoc directly.
      return updatedInventory; 
    } catch (error) {
      console.error('[REPOSITORY-INCREMENT-ITEM-QUANTITY] Error:', error);
      if (error instanceof NotFoundError) throw error;
      throw new Error(`Error incrementing item quantity in inventory: ${error.message}`);
    }
  }

  static async pushNewItemToInventory({ inventoryId, itemData, session }) {
    try {
      // Ensure the new sub-item has a unique _id before pushing
      const newSubItemId = new mongoose.Types.ObjectId();
      
      const itemWithId = { 
        ...itemData, 
        _id: newSubItemId,
      };

      console.log(`[REPOSITORY-PUSH-NEW-ITEM] Inventory ID: ${inventoryId}, New Item Data (with _id ${newSubItemId}):`, itemWithId);

      const updatedInventory = await Inventory.findOneAndUpdate(
        { _id: inventoryId },
        { $push: { items: itemWithId } },
        { new: true, session }
      );

      if (!updatedInventory) {
        throw new NotFoundError(`Could not find inventory with ID ${inventoryId} to push new item.`);
      }
      
      console.log('[REPOSITORY-PUSH-NEW-ITEM] Successfully pushed new item.');
      return {
        inventoryDoc: updatedInventory, // The raw updated inventory document
        newSubItemId: newSubItemId     // The _id of the item that was just added
      };
    } catch (error) {
      console.error('[REPOSITORY-PUSH-NEW-ITEM] Error:', error);
      if (error instanceof NotFoundError) throw error;
      throw new Error(`Error pushing new item to inventory: ${error.message}`);
    }
  }

  static async updateItemStatusAndMetadata({ ownerId, itemId, newStatus, newMetadata, session, order }) {
    try {
      const inventory = await Inventory.findOne({ ownerId });
      if (!inventory) {
        throw new NotFoundError(`Inventory for owner ${ownerId} not found.`);
      }
      const itemIndex = inventory.items.findIndex(
        invItem => invItem.itemId.toString() === itemId && invItem.order === order
      );
      if (itemIndex === -1) {
        throw new NotFoundError(`Item with ID ${itemId} not found in inventory.`);
      }
      if(newStatus) {
        inventory.items[itemIndex].status = newStatus;
      }
      if(newMetadata) {
        inventory.items[itemIndex].itemMetadata = newMetadata;
      }
      await (session ? inventory.save({ session }) : inventory.save());
      return InventoryRepository.findByOwnerId({ ownerId });
    } catch (error) {
      console.log('[REPOSITORY-UPDATE-ITEM-STATUS-AND-METADATA]', error);
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new Error(`Error updating item status and metadata in inventory: ${error.message}`);
    }
  }

  /**
   * Fixes inventory coherence (e.g., reorders items to be sequential)
   * @param {InventoryAggregate|Object} inventory - The inventory aggregate or plain object
   * @returns {Promise<InventoryAggregate>} - The fixed inventory aggregate
   */
  static async fixInventory(inventory) {
    // get populated inventory
    const populatedInventory = await Inventory.findById(inventory._id||inventory.id).populate('items.itemId');
    const parsedPopulatedInventoryItems = parsePopulatedItems(populatedInventory.items.toObject());
    // console.log('[REPOSITORY-FIX-INVENTORY] Populated inventory:', populatedInventory);
    // get aggregate
    const agg = InventoryAggregate.fromData({...populatedInventory.toObject(), items: parsedPopulatedInventoryItems});
    console.log('[REPOSITORY-FIX-INVENTORY] Aggregate:', agg);
    agg.normalizeOrders();
    // Save the fixed orders to the DB
    const dbInventory = await Inventory.findById(agg.id);
    if (!dbInventory) throw new NotFoundError(`Inventory with ID ${agg.id} not found for fixing.`);
    // Update each item's order
    agg.items.forEach(aggItem => {
      const dbItem = dbInventory.items.find(i => i.itemId.toString() === aggItem.itemId.toString());
      if (dbItem) dbItem.order = aggItem.order;
    });
    await dbInventory.save();
    return agg;
  }

  /**
   * Finds all inventories with filtering and pagination support
   * @param {Object} options - Query options
   * @param {Object} options.filter - MongoDB filter object
   * @param {number} options.limit - Maximum number of inventories to return
   * @param {number} options.skip - Number of inventories to skip for pagination
   * @param {boolean} options.isPopulated - Whether to populate item details
   * @returns {Promise<Object>} Object containing inventories and total count
   */
  static async findAllWithFilter({ filter = {}, limit = 50, skip = 0, isPopulated = false }) {
    try {
      console.log('[REPOSITORY-FIND-ALL-WITH-FILTER] Filter:', filter);
      
      // Build the query
      let query = Inventory.find(filter);
      
      // Add pagination
      query = query.skip(skip).limit(limit);
      
      // Add population if requested
      if (isPopulated) {
        query = query.populate('items.itemId');
      }
      
      // Execute query
      const inventories = await query.exec();
      
      // Get total count for pagination
      const totalCount = await Inventory.countDocuments(filter);
      
      console.log('[REPOSITORY-FIND-ALL-WITH-FILTER] Found inventories:', inventories.length);
      console.log('[REPOSITORY-FIND-ALL-WITH-FILTER] Total count:', totalCount);
      
      return {
        inventories: inventories.map(inv => inv.toObject()),
        totalCount
      };
    } catch (error) {
      console.log('[REPOSITORY-FIND-ALL-WITH-FILTER-ERROR]', error);
      throw new Error(`Error finding inventories with filter: ${error.message}`);
    }
  }

  /**
   * Finds all inventory items with status filtering across all inventories
   * @param {Object} options - Query options
   * @param {string} options.status - Required status filter
   * @param {string} [options.contractId] - Optional contract ID filter
   * @param {string} [options.itemType] - Optional item type filter
   * @param {string} [options.usageType] - Optional usage type filter
   * @param {string[]} [options.tags] - Optional tags filter
   * @param {number} [options.limit=50] - Maximum number of items to return
   * @param {number} [options.page=1] - Page number for pagination
   * @returns {Promise<Object>} Object containing filtered items and pagination info
   */
  static async findAllInventoryItemsWithStatusFilter({ 
    status, 
    contractId, 
    itemType, 
    usageType, 
    tags, 
    limit = 50, 
    page = 1 
  }) {
    try {
      console.log('[REPOSITORY-FIND-ALL-INVENTORY-ITEMS-WITH-STATUS-FILTER]', {
        status, contractId, itemType, usageType, tags, limit, page
      });

      // Validate required parameters
      if (!status) {
        throw new ValidationError('Status filter is required');
      }

      // Set pagination values
      const skip = (page - 1) * limit;
      const cappedLimit = Math.min(limit, 100); // Cap at 100 items per page

      // Build MongoDB filter for inventories
      const inventoryFilter = {
        'items.status': status
      };

      // Add contract ID filter if specified
      if (contractId !== undefined && contractId !== null) {
        inventoryFilter.contractId = contractId;
      } else {
        // If no contract ID specified, include both global (null) and contract-specific items
        inventoryFilter.$or = [
          { contractId: null },
          { contractId: { $exists: true, $ne: null } }
        ];
      }

      // Get all inventories with the specified filter
      const inventories = await Inventory.find(inventoryFilter)
        .populate('items.itemId')
        .skip(skip)
        .limit(cappedLimit)
        .exec();

      // Get total count for pagination
      const totalCount = await Inventory.countDocuments(inventoryFilter);

      // Process and filter items
      let allItems = [];

      for (const inventory of inventories) {
        const inventoryObj = inventory.toObject();
        
        // Filter items by status and other criteria
        let filteredItems = inventoryObj.items.filter(item => {
          // Status filter is already applied in the query
          if (itemType && item.itemId?.type !== itemType) {
            return false;
          }
          
          if (usageType && item.itemId?.usageType !== usageType) {
            return false;
          }
          
          if (tags && Array.isArray(tags) && tags.length > 0) {
            if (!item.itemId?.tags || !item.itemId.tags.some(tag => tags.includes(tag))) {
              return false;
            }
          }
          
          return true;
        });

        // Add inventory context to each item
        filteredItems = filteredItems.map(item => ({
          ...item,
          inventoryId: inventoryObj._id,
          ownerId: inventoryObj.ownerId,
          contractId: inventoryObj.contractId
        }));

        allItems = allItems.concat(filteredItems);
      }

      // Calculate pagination info
      const totalPages = Math.ceil(totalCount / cappedLimit);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      return {
        items: allItems,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: totalCount,
          itemsPerPage: cappedLimit,
          hasNextPage,
          hasPrevPage
        },
        filters: {
          status,
          contractId,
          itemType,
          usageType,
          tags
        }
      };

    } catch (error) {
      console.log('[REPOSITORY-FIND-ALL-INVENTORY-ITEMS-WITH-STATUS-FILTER-ERROR]', error);
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new Error(`Error finding inventory items with status filter: ${error.message}`);
    }
  }
}

export default InventoryRepository; 