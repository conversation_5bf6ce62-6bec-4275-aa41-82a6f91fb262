import { ShopModel } from "../model/shopSchema.js";
import { ShopAggregate } from "../aggregate/shopAggregate.js";
import {
  NotFoundError,
  ResourceAlreadyExistsError,
} from "../../../utils/customErrors/index.js";
import ItemModel from "../model/itemSchema.js";
import UserModel from "../../../domain/models/userModel.js";
import { SchoolModel } from "../../../domain/models/school/schoolModel.js";

export class ShopRepository {
  static async createShop({ contractId = null, itemIds = [] }) {
    // contractId is unique if present
    if (contractId) {
      const exists = await ShopModel.findOne({ contractId });
      if (exists)
        throw new ResourceAlreadyExistsError(
          "Shop with this contractId already exists"
        );
    }
    const parsedItems = itemIds.map((itemId, index) => ({
      item: itemId,
      order: index + 1,
    }));
    const shopDoc = await ShopModel.create({ contractId, items: parsedItems });
    return ShopAggregate.fromData({
      ...shopDoc.toObject(),
      id: shopDoc._id.toString(),
    });
  }

  static async addItemsToShop({ shopId, itemIds }) {
    if(!shopId) {
      shopId = null;
    }
    console.log({shopId,itemIds})
    let shopDoc = null;
    if(shopId) {
      shopDoc = await ShopModel.findById(shopId);
    } else {
      shopDoc = await ShopModel.findOne({ contractId: null });
    }
    console.log({shopDoc})
    if (!shopDoc) throw new NotFoundError("Shop not found");
    
    // Add new items (avoid duplicates by id)
    const existingIds = new Set(
      shopDoc.items.map((i) => i.item.id || i.item._id)
    );
    console.log({existingIds})
    // Filter out items that already exist in the shop
    const newItemIds = itemIds.filter(
      (itemId) => !existingIds.has(itemId)
    );
    console.log({newItemIds})
    // Fetch and validate that all items exist in the database
    const existingItems = await ItemModel.find({ _id: { $in: newItemIds } });
    console.log({existingItems})
    const foundItemIds = new Set(existingItems.map(item => item._id.toString()));
    console.log({foundItemIds})
    
    // Check for any items that don't exist
    const missingItemIds = newItemIds.filter(itemId => !foundItemIds.has(itemId));
    if (missingItemIds.length > 0) {
      throw new NotFoundError(`Items not found: ${missingItemIds.join(', ')}`);
    }
    console.log({missingItemIds})
    // Create new item entries with order and full item data
    const currentMaxOrder = shopDoc.items.length > 0 
      ? Math.max(...shopDoc.items.map(item => item.order || 0))
      : 0;
    console.log({currentMaxOrder})
    const newItems = existingItems.map((item, index) => ({
      item: item.toObject(), // Store the full item object, not just the ID
      order: currentMaxOrder + index + 1,
    }));
    console.log({newItems})
    shopDoc.items.push(...newItems);
    await shopDoc.save();
    console.log({shopDoc})
    return ShopAggregate.fromData({
      ...shopDoc.toObject(),
      id: shopDoc._id.toString(),
    });
  }

  static async removeItemsInShop({ shopId, itemIds }) {
    const shopDoc = await ShopModel.findById(shopId);
    if (!shopDoc) throw new NotFoundError("Shop not found");
    shopDoc.items = shopDoc.items.filter(
      (i) => !itemIds.includes(i.item.id || i.item._id)
    );
    await shopDoc.save();
    return ShopAggregate.fromData({
      ...shopDoc.toObject(),
      id: shopDoc._id.toString(),
    });
  }

  static async getShopItems({ userId }) {
    console.log({userId})
    const user = await UserModel.findById(userId);
    console.log({user})
    // find school through id or groupId
    const school = await SchoolModel.findOne({$or: [{_id: user?.schoolId}, {groupId: user?.schoolId}]}).lean();
    const userContractId = school?.contractId;
    console.log({userContractId,school})
    if(!userContractId) throw new NotFoundError("School not found");
    
    // Fetch global (contractId: null) and local (contractId: userContractId) shops
    const [globalShop, localShop] = await Promise.all([
      ShopModel.findOne({ contractId: null })
        .populate({
          path: 'items.item',
          model: 'Item'
        })
        .lean(),
      ShopModel.findOne({ contractId: userContractId })
        .populate({
          path: 'items.item',
          model: 'Item'
        })
        .lean()
    ]);

    console.log("Log1", JSON.stringify(globalShop, null, 2));
    console.log("Log2", JSON.stringify(localShop, null, 2));

    // Transform shops to DTO format
    const parsedGlobalShop = globalShop
      ? ShopAggregate.fromData({
          ...globalShop,
          id: globalShop._id.toString(),
        }).toDTO().items
      : [];

    const parsedLocalShop = localShop
      ? ShopAggregate.fromData({
          ...localShop,
          id: localShop._id.toString(),
        }).toDTO().items
      : [];

    console.log(
      JSON.stringify(
        {
          parsedGlobalShop,
          parsedLocalShop,
        },
        null,
        2
      )
    );

    return {
      GLOBAL: parsedGlobalShop,
      LOCAL: parsedLocalShop,
    };
  }
}
