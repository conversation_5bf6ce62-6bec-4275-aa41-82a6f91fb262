import { apiResponse } from "../../../../utils/apiResponse.js";
import { InvalidInputValueError } from "../../../../utils/customErrors/index.js";
import connectToDB from '../../../../infra/libs/mongodb/connect.js';
import disconnectFromDB from '../../../../infra/libs/mongodb/disconnect.js';

// Import shop handlers
import { handler as createShopHandler } from "./createShop.js";
import { handler as addItemsToShopHandler } from "./addItemsToShop.js";
import { handler as removeItemsInShopHandler } from "./removeItemsInShop.js";
import { handler as getShopItemsHandler } from "./getShopItems.js";

export async function handler(event) {
  await connectToDB();
  try {
    if (event.source === "serverless-plugin-warmup") {
      return Promise.resolve("Lambda warmed up!");
    }

    console.time("Shop Management Duration");
    const functions = {
      // Shop management endpoints
      "/shop": {
        POST: createShopHandler
      },
      "/shop/manage/items": {
        POST: addItemsToShopHandler,
        DELETE: removeItemsInShopHandler
      },
      "/shop/shops": {
        GET: getShopItemsHandler
      }
    };

    const resource = event.resource;
    const method = event.method || event.httpMethod;

    if (!functions[resource] || !functions[resource][method]) {
      throw new InvalidInputValueError(`Interface not mapped! for relative endpoint ${resource} with method : ${method}`);
    }

    const response = await functions[resource][method](event);
    console.log({response});
    console.timeEnd("Shop Management Duration");
    disconnectFromDB();
    return apiResponse(201, {body: response});
  } catch (error) {
    disconnectFromDB();
    console.log("ShopManagement", error.message);
    return Promise.resolve(
      apiResponse(error.code || 500, {
        body: {
          error: error.name || "UnexpectedError",
          message: error.message || error,
          formattedMessage:
            error.formattedMessage || "An unexpected error occurred.",
        },
      })
    );
  }
}
