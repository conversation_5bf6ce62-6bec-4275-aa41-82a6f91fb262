import { apiResponse } from "../../../../utils/apiResponse.js";
import { InvalidInputValueError } from "../../../../utils/customErrors/index.js";
import connectToDB from '../../../../infra/libs/mongodb/connect.js';
import disconnectFromDB from '../../../../infra/libs/mongodb/disconnect.js';

// Import inventory handlers
import { handler as createEmptyInventoryHandler } from "./createEmptyInventory.js";
import { handler as giveItemToInventoryHandler } from "./giveItemToInventory.js";
import { handler as makeInventoryTradeHandler } from "./makeInventoryTrade.js";
import { handler as getInventoryItemsHandler } from "./getInventoryItems.js";
import { handler as getAllInventoryItemsWithStatusFilterHandler } from "./getAllInventoryItemsWithStatusFilter.js";
import { handler as getUsersInventoriesResumeHandler } from "./getUsersInventoriesResume.js";
import { handler as getUserInventoriesTransactionsResumeHandler } from "./getUserInventoriesTransactionsResume.js";
import { handler as movementInventoryItemHandler } from "./movementInventoryItem.js";
import { handler as addBuyedItemFromShopHandler } from './addBuyedItemFromShop.js';
import { handler as adminMovementInventoryItemHandler } from './adminMovementInventoryItem.js';

export async function handler(event) {
  await connectToDB();
  try {
    if (event.source === "serverless-plugin-warmup") {
      return Promise.resolve("Lambda warmed up!");
    }

    console.time("Shop Inventory Management Duration");
    const functions = {
      // Inventory endpoints
      "/shop/inventory": {
        POST: createEmptyInventoryHandler,
        GET: getInventoryItemsHandler
      },
      "/shop/inventory/add-item": {
        POST: giveItemToInventoryHandler
      },
      "/shop/inventory/trade": {
        POST: makeInventoryTradeHandler
      },
      "/shop/inventory/move-item": {
        POST: movementInventoryItemHandler
      },
      "/shop/inventory/admin/move-item": {
        POST: adminMovementInventoryItemHandler
      },
      "/shop/inventory/user-resume": {
        GET: getUsersInventoriesResumeHandler
      },
      "/shop/inventory/transactions-resume": {
        GET: getUserInventoriesTransactionsResumeHandler
      },
      "/shop/inventory/buy-item": {
        POST: addBuyedItemFromShopHandler
      },
      "/shop/inventory/admin/items-with-status": {
        GET: getAllInventoryItemsWithStatusFilterHandler
      }
    };

    const resource = event.resource;
    const method = event.method || event.httpMethod;

    if (!functions[resource] || !functions[resource][method]) {
      throw new InvalidInputValueError(`Interface not mapped! for relative endpoint ${resource} with method : ${method}`);
    }

    const response = await functions[resource][method](event);
    console.log("[SHOP INVENTORY INTERFACE] RESPONSE",{response});
    console.timeEnd("Shop Inventory Management Duration");
    disconnectFromDB();
    return apiResponse(201, {body: response});
  } catch (error) {
    disconnectFromDB();
    console.log("ShopInventoryManagement error", error.message);
    return Promise.resolve(
      apiResponse(error.code || 500, {
        body: {
          error: error.name || "UnexpectedError",
          message: error.message || error,
          formattedMessage:
            error.formattedMessage || "An unexpected error occurred.",
        },
      })
    );
  }
}