import { handleStudentStageActivity } from '../useCases/handleStudentStageActivity.js';
import { apiResponse } from '../../../utils/apiResponse.js';
import connectToDB from '../../../infra/libs/mongodb/connect.js';

export async function handler(event) {
  try {
    await connectToDB();
    const body = JSON.parse((typeof event.body === 'string') ? event.body : JSON.stringify(event.body));
    console.log("body", JSON.stringify(body, null, 2));
    const { userId, pathId, stageOrder, completionStatus, stepId } = body;

    const activity = await handleStudentStageActivity({ 
      userId,
      pathId,
      stageOrder,
      completionStatus,
      stepId,
    });

    return apiResponse(200, { body: activity });
  } catch (error) {
    console.error("Error handling student stage activity:", error);
    return apiResponse(error.statusCode || 500, { body: { message: error.message } });
  }
} 