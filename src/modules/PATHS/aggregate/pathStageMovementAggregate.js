import { TypeValidator, CompositeValidator } from '../../../utils/validators/index.js';
import { ValidationError } from '../../../utils/customErrors/index.js';

// Define validators at the module level
const pathStageMovementTypesValidator = new TypeValidator({
    userId: 'string',
    pathId: 'string',
    stageOrder: 'number',
    completionProgress: 'number',
    isCompleted: 'boolean',
    completionInfo: 'array'
});

const completionInfoTypesValidator = new TypeValidator({
    stepId: 'string',
    completionStatus: 'number'
});

export class PathStageMovementAggregate {
    constructor({id, userId, pathId, stageOrder, completionProgress, isCompleted, completionInfo}) {
        this.id = id || null;
        this.userId = userId;
        this.pathId = pathId;
        this.stageOrder = stageOrder;
        this.completionProgress = completionProgress || 0;
        this.isCompleted = isCompleted || false;
        this.completionInfo = (completionInfo || []).map(info => ({
            stepId: info.stepId,
            completionStatus: info.completionStatus
        }));
    }

    static fromData(data) {
        let parsedData = {}
        // parse data : _id (objectId) to id (string) if not received directly id
        if (data._id && !data.id) parsedData.id = data._id.toString();
        // parse userId : (objectId) to (string)
        if (data.userId && !(data.userId instanceof String)) parsedData.userId = String(data.userId);
        // parse pathId : (objectId) to (string)
        if (data.pathId && !(data.pathId instanceof String)) parsedData.pathId = String(data.pathId);
        console.log({...data,...parsedData})
        const validator = new CompositeValidator(pathStageMovementTypesValidator);
        validator.validate({...data,...parsedData});
        
        // Validate each completion info item
        if (data.completionInfo) {
            data.completionInfo.forEach(info => {
                const infoValidator = new CompositeValidator(completionInfoTypesValidator);
                infoValidator.validate(info);
            });
        }
        const aggregate = new PathStageMovementAggregate(data);
        aggregate.adjustStageMovements();
        return aggregate;
    }
    
    // seek for duplicates of stepIds in completionInfo and remove them
    adjustStageMovements(){
        const uniqueStepIds = new Set();
        this.completionInfo = this.completionInfo.filter(info => {
            if (uniqueStepIds.has(info.stepId)) {
                return false;
            }
            uniqueStepIds.add(info.stepId);
            return true;
        });
    }

    updateCompletionInfo(stepId, completionStatus) {
        const existingStep = this.completionInfo.find(info => info.stepId === stepId);
        console.log("existingStep", existingStep);
        if (existingStep) {
            existingStep.completionStatus = completionStatus;
        } else {
            console.log("pushing new step", { stepId, completionStatus });
            this.completionInfo.push({ stepId, completionStatus });
        }
        this.updateCompletionProgress();
    }

    updateCompletionProgress() {
        this.adjustStageMovements();
        const completedSteps = this.completionInfo.filter(info => info.completionStatus === 1 || info.completionStatus === -1);
        this.completionProgress = this.completionInfo.length > 0 ? 
            completedSteps.length / this.completionInfo.length : 0;
    }

    isFullyCompleted(totalSteps) {
        return this.completionInfo.length === totalSteps && this.completionInfo.every(info => info.completionStatus === 1);
    }

    toData() {
        return {
            id: this.id,
            userId: this.userId,
            pathId: this.pathId,
            stageOrder: this.stageOrder,
            completionProgress: this.completionProgress,
            isCompleted: this.isCompleted,
            completionInfo: this.completionInfo
        };
    }

    toDTO() {
        return {
            id: this.id,
            userId: this.userId,
            pathId: this.pathId,
            stageOrder: this.stageOrder,
            completionProgress: this.completionProgress,
            isCompleted: this.isCompleted,
            completionInfo: this.completionInfo
        };
    }
} 