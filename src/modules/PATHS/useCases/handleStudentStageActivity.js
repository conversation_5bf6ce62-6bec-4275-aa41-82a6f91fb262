import { PathStageMovementRepository } from "../repositories/pathStageMovementRepository.js";
import { PathsRepository } from "../repositories/pathsRepository.js";
import { PathStageMovementAggregate } from "../aggregate/pathStageMovementAggregate.js";

const handleStudentStageActivity = async ({
  userId,
  pathId,
  stageOrder,
  completionStatus,
  stepId,
}) => {
  // check if completionStatus is valid
  if (
    completionStatus !== -1 &&
    completionStatus !== 0 &&
    completionStatus !== 1
  ) {
    throw new Error("Invalid completion status");
  }

  // check if stageOrder is inside the searched path
  const path = await PathsRepository.getPathById(pathId);

  if (!path) {
    throw new Error("Path not found");
  }

  const stage = path.getStageByOrder(stageOrder);
  if (!stage) {
    throw new Error("Stage order not found in the path");
  }

  // Find existing activity for the user and stage
  let movement = await PathStageMovementRepository.findOne({
    userId,
    pathId,
    stageOrder,
  });

  if (movement) {
    // Update completion info using aggregate method
    movement.updateCompletionInfo(stepId, completionStatus);
    // Check if all steps are completed
    movement.isCompleted = movement.isFullyCompleted(stage.numberOfSteps);
  } else {
    // Create new movement using aggregate
    movement = new PathStageMovementAggregate({
      userId,
      pathId,
      stageOrder,
      completionInfo: [{ stepId, completionStatus }],
      completionProgress: 0,
      isCompleted: false,
    });
    movement.updateCompletionProgress();
  }
  console.log("movement", JSON.stringify(movement, null, 2));
  // Save the movement
  const savedMovement = await PathStageMovementRepository.save(movement.toData());

  return savedMovement;
};

export { handleStudentStageActivity };
