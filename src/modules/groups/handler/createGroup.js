import { createGroup } from '../useCases/createGroup.js';

export async function handler(event) {
    const { name, description, parentGroupId, members, tags, category, contractId, initialMembers } = JSON.parse(event.body);

    const newGroup = await createGroup({
        name,
        description,
        parentGroupId,
        members,
        tags,
        category,
        contractId,
        initialMembers
    });

    return newGroup;
} 