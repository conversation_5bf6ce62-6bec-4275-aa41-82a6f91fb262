import { updateGroup } from '../useCases/updateGroup.js';

export async function handler(event) {
    const { groupId } = event.pathParameters;
    const { name, description, parentGroupId, tags, inactive } = JSON.parse(event.queryStringParameters);

    const updatedGroup = await updateGroup({
        groupId,
        name,
        description,
        parentGroupId,
        tags,
        inactive
    });

    return updatedGroup;
} 