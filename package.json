{"name": "front_end_atomize", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "predev": "node scripts/limpar-temp-uploads.js", "prestart": "node scripts/limpar-temp-uploads.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.688.0", "@aws-sdk/s3-request-presigner": "^3.688.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@headlessui/react": "^2.1.2", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^3.9.0", "@next/font": "^14.2.5", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@types/js-cookie": "^3.0.6", "@types/katex": "^0.16.7", "@types/react-latex": "^2.0.3", "@types/react-leaflet": "^3.0.0", "@uiw/react-md-editor": "^3.7.0", "@uploadthing/react": "^7.1.0", "axios": "^1.7.3", "browser-image-compression": "^2.0.2", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "framer-motion": "^11.5.4", "isomorphic-dompurify": "^2.26.0", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "katex": "^0.16.9", "leaflet": "^1.9.4", "lottie-react": "^2.4.0", "lucide-react": "^0.412.0", "moment": "^2.30.1", "next": "14.2.5", "next-themes": "^0.3.0", "openai": "^4.80.1", "pdf-lib": "^1.17.1", "pdfkit": "^0.16.0", "react": "^18", "react-beautiful-dnd": "^13.1.1", "react-calendar": "^5.0.0", "react-confetti": "^6.1.0", "react-dom": "^18", "react-hook-form": "^7.52.2", "react-icons": "^5.3.0", "react-image-crop": "^11.0.7", "react-katex": "^3.0.1", "react-latex": "^2.0.0", "react-leaflet": "^4.2.1", "react-lottie-player": "^2.1.0", "react-markdown": "^8.0.7", "react-medium-image-zoom": "^5.2.8", "react-router": "^6.26.0", "react-syntax-highlighter": "^15.5.0", "recharts": "^2.12.7", "rehype-katex": "^6.0.3", "rehype-sanitize": "^5.0.1", "rehype-stringify": "^9.0.3", "remark-math": "^5.1.1", "remark-parse": "^10.0.2", "remark-rehype": "^10.1.0", "sharp": "^0.33.5", "sonner": "^1.7.2", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "unified": "^10.1.2", "uploadthing": "^7.2.0", "uuid": "^11.0.5", "vaul": "^0.9.1", "zod": "^3.23.8"}, "devDependencies": {"@types/canvas-confetti": "^1.6.4", "@types/leaflet": "^1.9.16", "@types/node": "^20.14.14", "@types/react": "^18.3.3", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18", "@types/react-katex": "^3.0.4", "@types/tailwindcss": "^3.1.0", "@types/uuid": "^10.0.0", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwindcss": "^3.4.11", "typescript": "5.5.4"}}