// import { LoginTimeSeriesModel } from '../src/domain/models/tracking/loginTimeSeries.js';
// import { TrackingModel } from '../src/domain/models/tracking/trackingModel.js';
// import { QuestionMetricModel } from '../src/domain/models/metrics/questionMetricsModel.js';
// import { LessonMetricModel } from '../src/domain/models/metrics/lessonMetricsModel.js';
// import { UserModel } from '../src/domain/models/userModel.js';
// import { SchoolModel } from '../src/domain/models/school/schoolModel.js';
// import { GroupModel } from '../src/modules/groups/schema/groupSchema.js';
// import connectToDB from '../src/infra/libs/mongodb/connect.js';
// import fs from 'fs';
// import path from 'path';

// const exampleId = "67d649d1977ccf1f6bb2ce75";

// async function listAllUsersWithLogins() {
//     try {
//         console.log('\n=== Listing All Users with Login Activity ===');
        
//         // count total users
//         const totalUsers = await UserModel.countDocuments();
//         console.log(`Total users found: ${totalUsers}`);

//         // count total users with login activity
//         const totalUsersWithLoginActivity = await LoginTimeSeriesModel.distinct('metadata.userId');
//         console.log(`Total users with login activity (distinct): ${totalUsersWithLoginActivity.length}`);
        
//         // Debug: Check for null/undefined user IDs in login records
//         const loginRecordsWithNullUserId = await LoginTimeSeriesModel.countDocuments({
//             $or: [
//                 { 'metadata.userId': null },
//                 { 'metadata.userId': { $exists: false } },
//                 { 'metadata.userId': '' }
//             ]
//         });
//         console.log(`Login records with null/missing userId: ${loginRecordsWithNullUserId}`);

//         // Get all login records
//         const allLoginRecords = await LoginTimeSeriesModel.find({}).sort({ timestamp: -1 });
//         console.log(`Total login records found: ${allLoginRecords.length}`);
        
//         if (allLoginRecords.length === 0) {
//             console.log('No login records found in the database');
//             return;
//         }
        
//         // Group login records by user ID (filtering out null/undefined)
//         const userLoginMap = new Map();
//         let recordsWithValidUserId = 0;
//         let recordsWithInvalidUserId = 0;
        
//         allLoginRecords.forEach(record => {
//             const userId = record.metadata?.userId;
//             if (userId && userId.toString().trim() !== '') {
//                 recordsWithValidUserId++;
//                 const userIdStr = userId.toString();
//                 if (!userLoginMap.has(userIdStr)) {
//                     userLoginMap.set(userIdStr, []);
//                 }
//                 userLoginMap.get(userIdStr).push(record);
//             } else {
//                 recordsWithInvalidUserId++;
//                 console.log(`Invalid userId found in record: ${record._id}, metadata:`, record.metadata);
//             }
//         });
        
//         console.log(`Login records with valid userId: ${recordsWithValidUserId}`);
//         console.log(`Login records with invalid userId: ${recordsWithInvalidUserId}`);
//         console.log(`Unique users with login activity (from grouping): ${userLoginMap.size}`);
//         console.log(`\nUnique users with login activity: ${userLoginMap.size}`);
//         console.log('\n--- User Login Summary ---');
        
//         // Sort users by most recent activity
//         const sortedUsers = Array.from(userLoginMap.entries()).sort((a, b) => {
//             const aLatest = Math.max(...a[1].map(login => new Date(login.timestamp).getTime()));
//             const bLatest = Math.max(...b[1].map(login => new Date(login.timestamp).getTime()));
//             return bLatest - aLatest;
//         });
        
//         // Show first 10 users for brevity
//         for (let i = 0; i < Math.min(sortedUsers.length, 10); i++) {
//             const [userId, loginRecords] = sortedUsers[i];
//             const loginCount = loginRecords.length;
//             const latestLogin = loginRecords.reduce((latest, current) => 
//                 new Date(current.timestamp) > new Date(latest.timestamp) ? current : latest
//             );
//             const oldestLogin = loginRecords.reduce((oldest, current) => 
//                 new Date(current.timestamp) < new Date(oldest.timestamp) ? current : oldest
//             );
            
//             console.log(`\n${i + 1}. User ID: ${userId}`);
//             console.log(`   - Total logins: ${loginCount}`);
//             console.log(`   - Latest login: ${latestLogin.timestamp.toISOString()}`);
//             console.log(`   - Oldest login: ${oldestLogin.timestamp.toISOString()}`);
//             console.log(`   - Latest login type: ${latestLogin.name || 'Unknown'}`);
//             console.log(`   - Latest login source: ${latestLogin.metadata?.source || 'Unknown'}`);
            
//             // Show login types breakdown
//             const loginTypes = {};
//             loginRecords.forEach(record => {
//                 const type = record.name || 'Unknown';
//                 loginTypes[type] = (loginTypes[type] || 0) + 1;
//             });
            
//             console.log(`   - Login types:`, Object.entries(loginTypes).map(([type, count]) => `${type}(${count})`).join(', '));
//         }
        
//         if (sortedUsers.length > 10) {
//             console.log(`\n... and ${sortedUsers.length - 10} more users with login activity`);
//         }
        
//         // Show some interesting statistics
//         console.log('\n--- Statistics ---');
//         const totalLogins = allLoginRecords.length;
//         const usersWithLoginActivity = userLoginMap.size; // This is the actual users with login records
//         const avgLoginsPerUser = Math.round((totalLogins / usersWithLoginActivity) * 100) / 100;
//         const mostActiveUser = sortedUsers.reduce((most, current) => 
//             current[1].length > most[1].length ? current : most
//         );
        
//         const totalUniqueUsers = await UserModel.distinct('_id');
//         console.log(`Total users in database: ${totalUniqueUsers.length}`);
//         console.log(`Users with login activity: ${usersWithLoginActivity}`);
//         console.log(`Users without login activity: ${totalUniqueUsers.length - usersWithLoginActivity}`);
//         console.log(`Login activity coverage: ${Math.round((usersWithLoginActivity / totalUniqueUsers.length) * 100)}%`);
//         console.log(`Total login records: ${totalLogins}`);
//         console.log(`Average logins per active user: ${avgLoginsPerUser}`);
//         console.log(`Most active user: ${mostActiveUser[0]} (${mostActiveUser[1].length} logins)`);
        
//         // Show login types across all users
//         const allLoginTypes = {};
//         allLoginRecords.forEach(record => {
//             const type = record.name || 'Unknown';
//             allLoginTypes[type] = (allLoginTypes[type] || 0) + 1;
//         });
        
//         console.log('\n--- Login Types Distribution ---');
//         Object.entries(allLoginTypes)
//             .sort((a, b) => b[1] - a[1])
//             .forEach(([type, count]) => {
//                 const percentage = Math.round((count / totalLogins) * 100);
//                 console.log(`${type}: ${count} (${percentage}%)`);
//             });
            
//     } catch (error) {
//         console.error('Error listing users with logins:', error);
//     }
// }

// async function trackUserActivity() {
//     try {
//         // Connect to database
//         await connectToDB();
//         console.log('Connected to database');

//         // Get all login time series records for the user
//         console.log(`\n=== Fetching Login Time Series for userId: ${exampleId} ===`);
//         const loginRecords = await LoginTimeSeriesModel.find({
//             'metadata.userId': exampleId
//         }).sort({ timestamp: -1 }); // Sort by most recent first

//         console.log(`Found ${loginRecords.length} login records:`);
//         if (loginRecords.length > 0) {
//             loginRecords.forEach((record, index) => {
//                 console.log(`\n${index + 1}. Login Record:`);
//                 console.log(`   - Name: ${record.name}`);
//                 console.log(`   - Timestamp: ${record.timestamp.toISOString()}`);
//                 console.log(`   - Metadata:`, JSON.stringify(record.metadata, null, 2));
//             });
//         } else {
//             console.log('   No login records found for this user');
//         }

//         // Get tracking model data for the user
//         console.log(`\n=== Fetching Tracking Model for userId: ${exampleId} ===`);
//         const trackingRecord = await TrackingModel.findOne({
//             userId: exampleId
//         }).populate('lastVideo').populate('lastQuestion');

//         if (trackingRecord) {
//             console.log('Tracking Record Found:');
//             console.log(`   - User ID: ${trackingRecord.userId}`);
//             console.log(`   - Created At: ${trackingRecord.createdAt?.toISOString()}`);
//             console.log(`   - Updated At: ${trackingRecord.updatedAt?.toISOString()}`);
//             console.log(`   - Last Video ID: ${trackingRecord.lastVideo?._id || 'None'}`);
//             console.log(`   - Last Question ID: ${trackingRecord.lastQuestion?._id || 'None'}`);
            
//             if (trackingRecord.lastVideo) {
//                 console.log(`   - Last Video Details:`);
//                 console.log(`     * Video ID: ${trackingRecord.lastVideo.videoId}`);
//                 console.log(`     * Watched: ${trackingRecord.lastVideo.watched}`);
//                 console.log(`     * Created At: ${trackingRecord.lastVideo.createdAt?.toISOString()}`);
//                 console.log(`     * Updated At: ${trackingRecord.lastVideo.updatedAt?.toISOString()}`);
//             }
            
//             if (trackingRecord.lastQuestion) {
//                 console.log(`   - Last Question Details:`);
//                 console.log(`     * Question ID: ${trackingRecord.lastQuestion.questionId}`);
//                 console.log(`     * Is Correct: ${trackingRecord.lastQuestion.isCorrect}`);
//                 console.log(`     * Created At: ${trackingRecord.lastQuestion.createdAt?.toISOString()}`);
//                 console.log(`     * Updated At: ${trackingRecord.lastQuestion.updatedAt?.toISOString()}`);
//             }
//         } else {
//             console.log('No tracking record found for this user');
//         }

//         // Try to get some direct metrics data as well
//         console.log(`\n=== Fetching Direct Metrics Data ===`);
//         const questionCount = await QuestionMetricModel.countDocuments({ userId: exampleId });
//         const lessonCount = await LessonMetricModel.countDocuments({ userId: exampleId });
        
//         console.log(`Direct question metrics count: ${questionCount}`);
//         console.log(`Direct lesson metrics count: ${lessonCount}`);

//         if (questionCount > 0) {
//             const latestQuestion = await QuestionMetricModel.findOne({ userId: exampleId }).sort({ createdAt: -1 });
//             console.log(`Latest question: ${latestQuestion.createdAt?.toISOString()} (ID: ${latestQuestion.questionId}, Correct: ${latestQuestion.isCorrect})`);
//         }

//         if (lessonCount > 0) {
//             const latestLesson = await LessonMetricModel.findOne({ userId: exampleId }).sort({ createdAt: -1 });
//             console.log(`Latest lesson: ${latestLesson.createdAt?.toISOString()} (VideoID: ${latestLesson.videoId}, Watched: ${latestLesson.watched})`);
//         }

//         // Summary
//         console.log(`\n=== SUMMARY ===`);
//         console.log(`Total login records: ${loginRecords.length}`);
//         console.log(`Tracking record exists: ${trackingRecord ? 'Yes' : 'No'}`);
//         console.log(`Question metrics: ${questionCount}`);
//         console.log(`Lesson metrics: ${lessonCount}`);
        
//         if (loginRecords.length > 0) {
//             const mostRecentLogin = loginRecords[0];
//             console.log(`Most recent login: ${mostRecentLogin.timestamp.toISOString()}`);
//             console.log(`Oldest login: ${loginRecords[loginRecords.length - 1].timestamp.toISOString()}`);
//         }

//         // Check if this user has any activity at all
//         const hasAnyActivity = loginRecords.length > 0 || trackingRecord || questionCount > 0 || lessonCount > 0;
//         console.log(`\nUser has activity data: ${hasAnyActivity ? 'Yes' : 'No'}`);

//     } catch (error) {
//         console.error('Error fetching user activity data:', error);
//     }
// }

// async function checkOrphanedLoginRecords() {
//     try {
//         console.log('\n=== Checking for Orphaned Login Records ===');
        
//         // Get all distinct user IDs from login records
//         const userIdsWithLogins = await LoginTimeSeriesModel.distinct('metadata.userId');
//         console.log(`User IDs found in login records: ${userIdsWithLogins.length}`);
        
//         // Get all user IDs from users table
//         const existingUserIds = await UserModel.distinct('_id');
//         const existingUserIdStrings = existingUserIds.map(id => id.toString());
//         console.log(`User IDs found in users table: ${existingUserIdStrings.length}`);
        
//         // Find user IDs in logins that don't exist in users table
//         const orphanedUserIds = userIdsWithLogins.filter(loginUserId => 
//             !existingUserIdStrings.includes(loginUserId.toString())
//         );
        
//         console.log(`\nOrphaned login records (user IDs not in users table): ${orphanedUserIds.length}`);
        
//         if (orphanedUserIds.length > 0) {
//             console.log('\n--- Orphaned User IDs ---');
            
//             for (let i = 0; i < Math.min(orphanedUserIds.length, 20); i++) { // Show first 20
//                 const orphanedId = orphanedUserIds[i];
                
//                 // Get login records for this orphaned ID
//                 const loginRecords = await LoginTimeSeriesModel.find({
//                     'metadata.userId': orphanedId
//                 }).sort({ timestamp: -1 });
                
//                 const loginCount = loginRecords.length;
//                 const latestLogin = loginRecords[0];
//                 const oldestLogin = loginRecords[loginRecords.length - 1];
                
//                 console.log(`\n${i + 1}. Orphaned User ID: ${orphanedId}`);
//                 console.log(`   - Login records: ${loginCount}`);
//                 console.log(`   - Latest login: ${latestLogin.timestamp.toISOString()}`);
//                 console.log(`   - Oldest login: ${oldestLogin.timestamp.toISOString()}`);
//                 console.log(`   - Latest login type: ${latestLogin.name || 'Unknown'}`);
//                 console.log(`   - Latest login source: ${latestLogin.metadata?.source || 'Unknown'}`);
//             }
            
//             if (orphanedUserIds.length > 20) {
//                 console.log(`\n... and ${orphanedUserIds.length - 20} more orphaned user IDs`);
//             }
            
//             // Show statistics about orphaned records
//             console.log('\n--- Orphaned Records Statistics ---');
            
//             let totalOrphanedLogins = 0;
//             for (const orphanedId of orphanedUserIds) {
//                 const count = await LoginTimeSeriesModel.countDocuments({
//                     'metadata.userId': orphanedId
//                 });
//                 totalOrphanedLogins += count;
//             }
            
//             console.log(`Total orphaned login records: ${totalOrphanedLogins}`);
//             console.log(`Percentage of total logins that are orphaned: ${Math.round((totalOrphanedLogins / userIdsWithLogins.length) * 100)}%`);
            
//             // Find the most active orphaned user
//             let mostActiveOrphanedId = null;
//             let maxOrphanedLogins = 0;
            
//             for (const orphanedId of orphanedUserIds.slice(0, 10)) { // Check first 10 for performance
//                 const count = await LoginTimeSeriesModel.countDocuments({
//                     'metadata.userId': orphanedId
//                 });
//                 if (count > maxOrphanedLogins) {
//                     maxOrphanedLogins = count;
//                     mostActiveOrphanedId = orphanedId;
//                 }
//             }
            
//             if (mostActiveOrphanedId) {
//                 console.log(`Most active orphaned user (from first 10 checked): ${mostActiveOrphanedId} (${maxOrphanedLogins} logins)`);
//             }
            
//         } else {
//             console.log('\n✅ No orphaned login records found - all login user IDs exist in users table');
//         }
        
//         // Also check the reverse - users without any login activity
//         const usersWithoutLogins = existingUserIdStrings.filter(userId => 
//             !userIdsWithLogins.map(id => id.toString()).includes(userId)
//         );
        
//         console.log(`\n--- Users Without Login Activity ---`);
//         console.log(`Users in database without any login records: ${usersWithoutLogins.length}`);
//         console.log(`Percentage of users never logged in: ${Math.round((usersWithoutLogins.length / existingUserIdStrings.length) * 100)}%`);
        
//     } catch (error) {
//         console.error('Error checking orphaned login records:', error);
//     }
// }

// async function listUsersWithLearningActivity() {
//     try {
//         console.log('\n=== Listing Users with Learning Activity (Questions & Lessons) ===');
        
//         // Get users with question metrics
//         const usersWithQuestions = await QuestionMetricModel.distinct('userId');
//         console.log(`Users with question metrics: ${usersWithQuestions.length}`);
        
//         // Get users with lesson metrics  
//         const usersWithLessons = await LessonMetricModel.distinct('userId');
//         console.log(`Users with lesson metrics: ${usersWithLessons.length}`);
        
//         // Get total counts
//         const totalQuestionMetrics = await QuestionMetricModel.countDocuments();
//         const totalLessonMetrics = await LessonMetricModel.countDocuments();
//         console.log(`Total question metrics: ${totalQuestionMetrics}`);
//         console.log(`Total lesson metrics: ${totalLessonMetrics}`);
        
//         // Combine to get users with any learning activity
//         const allLearningUserIds = new Set([
//             ...usersWithQuestions.map(id => id.toString()),
//             ...usersWithLessons.map(id => id.toString())
//         ]);
        
//         console.log(`\nUsers with any learning activity: ${allLearningUserIds.size}`);
        
//         // Get users with both question AND lesson activity
//         const questionUserStrings = usersWithQuestions.map(id => id.toString());
//         const lessonUserStrings = usersWithLessons.map(id => id.toString());
//         const usersWithBothActivities = questionUserStrings.filter(userId => 
//             lessonUserStrings.includes(userId)
//         );
//         console.log(`Users with both questions AND lessons: ${usersWithBothActivities.length}`);
        
//         // Show detailed breakdown for top active users
//         console.log('\n--- Top Learning Activity Users ---');
        
//         // Get activity counts for each user
//         const userActivityStats = [];
        
//         for (const userId of Array.from(allLearningUserIds).slice(0, 20)) { // First 20 for performance
//             const questionCount = await QuestionMetricModel.countDocuments({ userId });
//             const lessonCount = await LessonMetricModel.countDocuments({ userId });
            
//             // Get latest activities
//             const latestQuestion = await QuestionMetricModel.findOne({ userId }).sort({ createdAt: -1 });
//             const latestLesson = await LessonMetricModel.findOne({ userId }).sort({ createdAt: -1 });
            
//             const latestQuestionTime = latestQuestion ? new Date(latestQuestion.createdAt).getTime() : 0;
//             const latestLessonTime = latestLesson ? new Date(latestLesson.createdAt).getTime() : 0;
//             const latestActivityTime = Math.max(latestQuestionTime, latestLessonTime);
            
//             userActivityStats.push({
//                 userId,
//                 questionCount,
//                 lessonCount,
//                 totalActivity: questionCount + lessonCount,
//                 latestActivityTime,
//                 latestQuestion,
//                 latestLesson
//             });
//         }
        
//         // Sort by total activity
//         userActivityStats.sort((a, b) => b.totalActivity - a.totalActivity);
        
//         // Show top 10
//         for (let i = 0; i < Math.min(userActivityStats.length, 10); i++) {
//             const user = userActivityStats[i];
//             console.log(`\n${i + 1}. User ID: ${user.userId}`);
//             console.log(`   - Question metrics: ${user.questionCount}`);
//             console.log(`   - Lesson metrics: ${user.lessonCount}`);
//             console.log(`   - Total learning activities: ${user.totalActivity}`);
            
//             if (user.latestActivityTime > 0) {
//                 console.log(`   - Latest activity: ${new Date(user.latestActivityTime).toISOString()}`);
//             }
            
//             if (user.latestQuestion) {
//                 console.log(`   - Latest question: ID ${user.latestQuestion.questionId}, Correct: ${user.latestQuestion.isCorrect}, Date: ${user.latestQuestion.createdAt.toISOString()}`);
//             }
            
//             if (user.latestLesson) {
//                 console.log(`   - Latest lesson: Video ${user.latestLesson.videoId}, Watched: ${user.latestLesson.watched}, Date: ${user.latestLesson.createdAt.toISOString()}`);
//             }
//         }
        
//         if (userActivityStats.length > 10) {
//             console.log(`\n... and ${userActivityStats.length - 10} more users with learning activity`);
//         }
        
//         // Calculate averages
//         const avgQuestionsPerUser = usersWithQuestions.length > 0 ? 
//             Math.round((totalQuestionMetrics / usersWithQuestions.length) * 100) / 100 : 0;
//         const avgLessonsPerUser = usersWithLessons.length > 0 ? 
//             Math.round((totalLessonMetrics / usersWithLessons.length) * 100) / 100 : 0;
        
//         // Get comparison with total users and login users
//         const totalUsers = await UserModel.countDocuments();
//         const loginUsers = await LoginTimeSeriesModel.distinct('metadata.userId');
        
//         console.log('\n--- Learning Activity Statistics ---');
//         console.log(`Total users in database: ${totalUsers}`);
//         console.log(`Users with login activity: ${loginUsers.length}`);
//         console.log(`Users with learning activity: ${allLearningUserIds.size}`);
//         console.log(`Users with only login (no learning): ${loginUsers.length - allLearningUserIds.size}`);
//         console.log(`\nLearning engagement rate: ${Math.round((allLearningUserIds.size / totalUsers) * 100)}%`);
//         console.log(`Learning activity among login users: ${Math.round((allLearningUserIds.size / loginUsers.length) * 100)}%`);
        
//         console.log(`\nQuestion activity breakdown:`);
//         console.log(`  - Users with questions: ${usersWithQuestions.length}`);
//         console.log(`  - Total question metrics: ${totalQuestionMetrics}`);
//         console.log(`  - Average questions per active user: ${avgQuestionsPerUser}`);
        
//         console.log(`\nLesson activity breakdown:`);
//         console.log(`  - Users with lessons: ${usersWithLessons.length}`);
//         console.log(`  - Total lesson metrics: ${totalLessonMetrics}`);
//         console.log(`  - Average lessons per active user: ${avgLessonsPerUser}`);
        
//         console.log(`\nActivity overlap:`);
//         console.log(`  - Users with only questions: ${usersWithQuestions.length - usersWithBothActivities.length}`);
//         console.log(`  - Users with only lessons: ${usersWithLessons.length - usersWithBothActivities.length}`);
//         console.log(`  - Users with both questions & lessons: ${usersWithBothActivities.length}`);
        
//         // Find most active question user
//         if (userActivityStats.length > 0) {
//             const mostActiveQuestionUser = userActivityStats.reduce((most, current) => 
//                 current.questionCount > most.questionCount ? current : most
//             );
//             const mostActiveLessonUser = userActivityStats.reduce((most, current) => 
//                 current.lessonCount > most.lessonCount ? current : most
//             );
            
//             console.log(`\nMost active question user: ${mostActiveQuestionUser.userId} (${mostActiveQuestionUser.questionCount} questions)`);
//             console.log(`Most active lesson user: ${mostActiveLessonUser.userId} (${mostActiveLessonUser.lessonCount} lessons)`);
//         }
        
//     } catch (error) {
//         console.error('Error listing users with learning activity:', error);
//     }
// }

// async function listAllSchools() {
//     try {
//         console.log('\n=== Listing All Schools ===');
        
//         // Get all schools without populating to avoid schema errors
//         const schools = await SchoolModel.find({});
//         console.log(`Total schools found: ${schools.length}`);
        
//         if (schools.length === 0) {
//             console.log('No schools found in the database');
//             return;
//         }
        
//         // Count active vs inactive schools
//         const activeSchools = schools.filter(school => !school.inactive);
//         const inactiveSchools = schools.filter(school => school.inactive);
        
//         console.log(`Active schools: ${activeSchools.length}`);
//         console.log(`Inactive schools: ${inactiveSchools.length}`);
        
//         console.log('\n--- School List ---');
        
//         // Sort schools by name
//         const sortedSchools = schools.sort((a, b) => a.name.localeCompare(b.name));
        
//         for (let i = 0; i < sortedSchools.length; i++) {
//             const school = sortedSchools[i];
            
//             console.log(`\n${i + 1}. School ID: ${school._id}`);
//             console.log(`   - Name: ${school.name}`);
//             console.log(`   - Address: ${school.address}`);
//             console.log(`   - Status: ${school.inactive ? 'Inactive' : 'Active'}`);
//             console.log(`   - Contract ID: ${school.contractId || 'No contract'}`);
//             console.log(`   - Group ID: ${school.groupId || 'No group'}`);
//             console.log(`   - Students CPF count: ${school.studentsCPF ? school.studentsCPF.length : 0}`);
//             console.log(`   - Coordinates: ${school.coordinates?.lat || 0}, ${school.coordinates?.long || 0}`);
//             console.log(`   - Created: ${school.createdAt ? school.createdAt.toISOString() : 'Unknown'}`);
//             console.log(`   - Updated: ${school.updatedAt ? school.updatedAt.toISOString() : 'Unknown'}`);
//         }
        
//         // Show schools by status - simplified list with names and IDs
//         if (activeSchools.length > 0) {
//             console.log('\n--- Active Schools (Names & IDs) ---');
//             activeSchools.forEach((school, index) => {
//                 console.log(`${index + 1}. ${school.name} (ID: ${school._id})`);
//             });
//         }
        
//         if (inactiveSchools.length > 0) {
//             console.log('\n--- Inactive Schools (Names & IDs) ---');
//             inactiveSchools.forEach((school, index) => {
//                 console.log(`${index + 1}. ${school.name} (ID: ${school._id})`);
//             });
//         }
        
//         // Statistics
//         console.log('\n--- School Statistics ---');
//         console.log(`Total schools: ${schools.length}`);
//         console.log(`Active schools: ${activeSchools.length} (${Math.round((activeSchools.length / schools.length) * 100)}%)`);
//         console.log(`Inactive schools: ${inactiveSchools.length} (${Math.round((inactiveSchools.length / schools.length) * 100)}%)`);
        
//         // Count schools with students
//         const schoolsWithStudents = schools.filter(school => 
//             school.studentsCPF && school.studentsCPF.length > 0
//         );
//         console.log(`Schools with students: ${schoolsWithStudents.length}`);
        
//         if (schoolsWithStudents.length > 0) {
//             const totalStudents = schoolsWithStudents.reduce((sum, school) => 
//                 sum + (school.studentsCPF ? school.studentsCPF.length : 0), 0
//             );
//             const avgStudentsPerSchool = Math.round((totalStudents / schoolsWithStudents.length) * 100) / 100;
            
//             console.log(`Total students across all schools: ${totalStudents}`);
//             console.log(`Average students per school (with students): ${avgStudentsPerSchool}`);
            
//             // Find school with most students
//             const schoolWithMostStudents = schoolsWithStudents.reduce((most, current) => 
//                 (current.studentsCPF?.length || 0) > (most.studentsCPF?.length || 0) ? current : most
//             );
            
//             console.log(`School with most students: ${schoolWithMostStudents.name} (${schoolWithMostStudents.studentsCPF?.length || 0} students)`);
//         }
        
//         // Check for schools without coordinates
//         const schoolsWithoutCoords = schools.filter(school => 
//             !school.coordinates || (school.coordinates.lat === 0 && school.coordinates.long === 0)
//         );
        
//         if (schoolsWithoutCoords.length > 0) {
//             console.log(`\nSchools without proper coordinates: ${schoolsWithoutCoords.length}`);
//         }
        
//     } catch (error) {
//         console.error('Error listing schools:', error);
//     }
// }

// async function checkTeachersInGroups() {
//     try {
//         console.log('\n=== Checking Teachers in School Groups ===');
        
//         // Read teachers.json file
//         const teachersFilePath = path.join(process.cwd(), '@scripts', 'teachers', 'teachers.json');
        
//         if (!fs.existsSync(teachersFilePath)) {
//             console.error(`Teachers file not found at: ${teachersFilePath}`);
//             return;
//         }
        
//         const teachersData = JSON.parse(fs.readFileSync(teachersFilePath, 'utf8'));
//         console.log(`Loaded ${teachersData.length} teachers from teachers.json`);
        
//         // Get all schools and groups for reference
//         const schools = await SchoolModel.find({});
//         const groups = await GroupModel.find({}).populate('members');
        
//         console.log(`Found ${schools.length} schools and ${groups.length} groups in database`);
        
//         // Create maps for quick lookup
//         const schoolMap = new Map();
//         schools.forEach(school => {
//             schoolMap.set(school._id.toString(), school);
//         });
        
//         const groupMap = new Map();
//         groups.forEach(group => {
//             groupMap.set(group._id.toString(), group);
//         });
        
//         console.log('\n--- Teacher-School-Group Analysis ---');
        
//         let teachersFound = 0;
//         let teachersNotFound = 0;
//         let teachersInCorrectGroup = 0;
//         let teachersNotInGroup = 0;
//         let schoolsWithoutGroup = 0;
//         let groupsNotFound = 0;
        
//         for (let i = 0; i < teachersData.length; i++) {
//             const teacher = teachersData[i];
            
//             console.log(`\n${i + 1}. Teacher: ${teacher.name} (${teacher.email})`);
//             console.log(`   - School: ${teacher.school}`);
//             console.log(`   - School ID: ${teacher.schoolId}`);
            
//             // Check if teacher exists in database
//             const teacherUser = await UserModel.findOne({ email: teacher.email });
            
//             if (!teacherUser) {
//                 console.log(`   ❌ Teacher not found in database`);
//                 teachersNotFound++;
//                 continue;
//             }
            
//             teachersFound++;
//             console.log(`   ✅ Teacher found in database (User ID: ${teacherUser._id})`);
//             console.log(`   - Name in DB: ${teacherUser.name}`);
//             console.log(`   - Role in DB: ${teacherUser.role}`);
            
//             // Check if school exists
//             const school = schoolMap.get(teacher.schoolId);
//             if (!school) {
//                 console.log(`   ❌ School not found with ID: ${teacher.schoolId}`);
//                 continue;
//             }
            
//             console.log(`   ✅ School found: ${school.name}`);
//             console.log(`   - School Group ID: ${school.groupId || 'No group assigned'}`);
            
//             // Check if school has a group
//             if (!school.groupId) {
//                 console.log(`   ⚠️  School has no group assigned`);
//                 schoolsWithoutGroup++;
//                 continue;
//             }
            
//             // Check if group exists
//             const group = groupMap.get(school.groupId.toString());
//             if (!group) {
//                 console.log(`   ❌ Group not found with ID: ${school.groupId}`);
//                 groupsNotFound++;
//                 continue;
//             }
            
//             console.log(`   ✅ Group found: ${group.name}`);
//             console.log(`   - Group Category: ${group.category}`);
//             console.log(`   - Group Members Count: ${group.members ? group.members.length : 0}`);
            
//             // Check if teacher is in the group
//             const teacherInGroup = group.members && group.members.some(member => 
//                 member._id.toString() === teacherUser._id.toString()
//             );
            
//             if (teacherInGroup) {
//                 console.log(`   ✅ Teacher IS in the correct group`);
//                 teachersInCorrectGroup++;
//             } else {
//                 console.log(`   ❌ Teacher is NOT in the group`);
//                 teachersNotInGroup++;
                
//                 // Show group members for debugging
//                 if (group.members && group.members.length > 0) {
//                     console.log(`   - Group members (first 5):`);
//                     for (let j = 0; j < Math.min(group.members.length, 5); j++) {
//                         const member = group.members[j];
//                         console.log(`     * ${member.name} (${member.email}) - Role: ${member.role}`);
//                     }
//                     if (group.members.length > 5) {
//                         console.log(`     ... and ${group.members.length - 5} more members`);
//                     }
//                 } else {
//                     console.log(`   - Group has no members`);
//                 }
//             }
//         }
        
//         console.log('\n=== Summary ===');
//         console.log(`Total teachers in JSON file: ${teachersData.length}`);
//         console.log(`Teachers found in database: ${teachersFound}`);
//         console.log(`Teachers NOT found in database: ${teachersNotFound}`);
//         console.log(`Schools without group: ${schoolsWithoutGroup}`);
//         console.log(`Groups not found: ${groupsNotFound}`);
//         console.log(`Teachers in correct group: ${teachersInCorrectGroup}`);
//         console.log(`Teachers NOT in group: ${teachersNotInGroup}`);
        
//         console.log('\n=== Rates ===');
//         if (teachersFound > 0) {
//             const groupAssignmentRate = Math.round((teachersInCorrectGroup / teachersFound) * 100);
//             console.log(`Teacher-to-group assignment rate: ${groupAssignmentRate}%`);
//         }
        
//         if (teachersData.length > 0) {
//             const dbExistenceRate = Math.round((teachersFound / teachersData.length) * 100);
//             console.log(`Teacher database existence rate: ${dbExistenceRate}%`);
//         }
        
//         // Additional analysis - find teachers in other groups
//         console.log('\n--- Teachers in Other Groups ---');
        
//         const teachersInOtherGroups = [];
        
//         for (const teacher of teachersData) {
//             const teacherUser = await UserModel.findOne({ email: teacher.email });
//             if (!teacherUser) continue;
            
//             // Find all groups this teacher is in
//             const teacherGroups = groups.filter(group => 
//                 group.members && group.members.some(member => 
//                     member._id.toString() === teacherUser._id.toString()
//                 )
//             );
            
//             if (teacherGroups.length > 0) {
//                 const school = schoolMap.get(teacher.schoolId);
//                 const expectedGroupId = school?.groupId?.toString();
                
//                 const otherGroups = teacherGroups.filter(group => 
//                     group._id.toString() !== expectedGroupId
//                 );
                
//                 if (otherGroups.length > 0) {
//                     teachersInOtherGroups.push({
//                         teacher: teacher,
//                         user: teacherUser,
//                         expectedGroup: expectedGroupId,
//                         actualGroups: teacherGroups,
//                         otherGroups: otherGroups
//                     });
//                 }
//             }
//         }
        
//         if (teachersInOtherGroups.length > 0) {
//             console.log(`Found ${teachersInOtherGroups.length} teachers in unexpected groups:`);
            
//             teachersInOtherGroups.forEach((item, index) => {
//                 console.log(`\n${index + 1}. ${item.teacher.name} (${item.teacher.email})`);
//                 console.log(`   - Expected group: ${item.expectedGroup || 'None'}`);
//                 console.log(`   - Actually in groups:`);
//                 item.actualGroups.forEach(group => {
//                     console.log(`     * ${group.name} (${group._id}) - Category: ${group.category}`);
//                 });
//             });
//         } else {
//             console.log('No teachers found in unexpected groups');
//         }
        
//     } catch (error) {
//         console.error('Error checking teachers in groups:', error);
//     }
// }

// async function main() {
//     try {
//         // Connect to database
//         await connectToDB();
//         console.log('Connected to database');
        
//         // Check teachers in groups
//         await checkTeachersInGroups();
        
//         // List all schools
//         // await listAllSchools();
        
//         // First, check for orphaned login records
//         // await checkOrphanedLoginRecords();
        
//         // Then, list all users with login activity
//         // await listAllUsersWithLogins();
        
//         // Next, analyze learning activity (questions & lessons)
//         // await listUsersWithLearningActivity();
        
//         // Finally, analyze specific user
//         // await trackUserActivity();
        
//     } catch (error) {
//         console.error('Error in main function:', error);
//     } finally {
//         process.exit(0);
//     }
// }

// // Run the script
// main();