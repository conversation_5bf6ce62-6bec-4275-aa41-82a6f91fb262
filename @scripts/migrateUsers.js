import csv from 'csv-parser';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import mongoose from 'mongoose';
import {createManyUsersService} from '../src/application/services/user/create.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Add MongoDB connection setup
async function connectToDatabase() {
    try {
        const connectionString = 'connection-string';
        if (!connectionString) {
            throw new Error('CONNECTION_STRING environment variable is not defined');
        }
        
        await mongoose.connect(connectionString);
    } catch (error) {
        console.error('Failed to connect to MongoDB:', error);
        process.exit(1);
    }
}

async function migrateUsers(csvFilePath, schoolId, separator='\t') {
    const results = [];
    
    // Read and parse CSV
    await new Promise((resolve, reject) => {
        fs.createReadStream(csvFilePath)
            .pipe(csv({
                headers: ['Nome', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON> Formatado', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
                separator: separator
            }))
            .on('data', (data) => results.push(data))
            .on('end', resolve)
            .on('error', reject);
    });

    console.log(`Found ${results.length} users to migrate in school : ${results[0].Escola}`);

    // Transform all rows into userData objects
    const usersToCreate = results.map((row, index) => {
        const userData = {
            name: row.Nome,
            username: row.Username,
            email: row['Email Formatado'] || row.Email,
            password: row.Senha,
            school: row.Escola,
            role: 'student',
            enrollmentId: row['Código'],
            schoolId: schoolId,
            CPF: '',
        };
        // console.log(`Prepared user ${index + 1}/${results.length}:`, userData.email);
        return userData;
    });

    try {
        // Bulk create all users
        const users = await createManyUsersService(usersToCreate);
        console.log(`Successfully created ${users.length} users`);
    } catch (error) {
        console.error('Failed to create users:', error.message);
        throw error;
    }
}

// Load configuration from mock file if no arguments provided
async function loadConfig() {
    const args = process.argv.slice(2);
    
    if (args.length >= 2) {
        return {
            csvPath: args[0],
            schoolId: args[1]
        };
    }

    try {
        const mockPath = path.join(__dirname, '..', '@mocks', 'users.json');
        const mockConfig = JSON.parse(fs.readFileSync(mockPath, 'utf8'));
        return mockConfig;
    } catch (error) {
        console.error('Error loading mock configuration:', error);
        process.exit(1);
    }
}

// Update main execution to include database connection
connectToDatabase()
    .then(() => loadConfig())
    .then(config => {
        if (!config.csvPath || !config.schoolId) {
            throw new Error('Missing required configuration: csvPath and schoolId');
        }
        return migrateUsers(config.csvPath, config.schoolId);
    })
    .then(() => {
        console.log('Migration completed');
        return mongoose.connection.close();
    })
    .catch(error => {
        console.error('Migration failed:', error);
        mongoose.connection.close();
    }); 