import mongoose from 'mongoose';
import fs from 'fs';
import path from 'path';
import { LoginTimeSeriesModel } from '../src/domain/models/tracking/loginTimeSeries.js';
import { XpTimeSeriesModel } from '../src/domain/models/tracking/xpTimeSeries.js';
import { UserModel } from '../src/domain/models/userModel.js';
import { SchoolModel } from '../src/domain/models/school/schoolModel.js';
import { QuestionMetricModel } from '../src/domain/models/metrics/questionMetricsModel.js';
import { LessonMetricModel } from '../src/domain/models/metrics/lessonMetricsModel.js';
import { TrackingModel } from '../src/domain/models/tracking/trackingModel.js';
import dotenv from 'dotenv';

dotenv.config();

const CONNECTION_STRING = process.env.CONNECTION_STRING;

// Conecta ao MongoDB
async function conectarAoMongoDB(mongodbUrl) {
  try {
    await mongoose.connect(mongodbUrl, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Conectado ao MongoDB');
  } catch (error) {
    console.error('Falha ao conectar ao MongoDB:', error);
    process.exit(1);
  }
}

// Configura o período de análise
function obterPeriodoDeAnalise(mesesAtras = 0) {
  const dataFinal = new Date();
  dataFinal.setHours(23, 59, 59, 999); // Final do mês atual
  
  // Ajusta para o final do mês desejado
  dataFinal.setMonth(dataFinal.getMonth() - mesesAtras);
  dataFinal.setDate(1); // Primeiro dia do mês
  dataFinal.setHours(0, 0, 0, 0);
  
  const dataInicial = new Date(dataFinal);
  dataInicial.setMonth(dataInicial.getMonth() - 1); // Mês anterior
  
  return { dataInicial, dataFinal };
}

// Configura o período de análise diário
function obterPeriodoDeAnaliseDiario(diasAtras = 0) {
  const dataFinal = new Date();
  dataFinal.setHours(23, 59, 59, 999); // Final do dia atual
  dataFinal.setDate(dataFinal.getDate() - diasAtras);
  
  const dataInicial = new Date(dataFinal);
  dataInicial.setHours(0, 0, 0, 0); // Início do dia
  
  return { dataInicial, dataFinal };
}

// Formata data para o formato brasileiro (UTC-3)
function formatarDataBR(dataString) {
  if (!dataString) return '';
  
  const data = new Date(dataString);
  // Subtrai 3 horas para o fuso horário brasileiro
  data.setHours(data.getHours() - 3);
  
  const dia = String(data.getDate()).padStart(2, '0');
  const mes = String(data.getMonth() + 1).padStart(2, '0');
  const ano = data.getFullYear();
  const horas = String(data.getHours()).padStart(2, '0');
  const minutos = String(data.getMinutes()).padStart(2, '0');
  
  return `${dia}/${mes}/${ano} ${horas}:${minutos}`;
}

// Formata data curta (dia/mês/ano)
function formatarDataCurta(data) {
  const dia = String(data.getDate()).padStart(2, '0');
  const mes = String(data.getMonth() + 1).padStart(2, '0');
  const ano = data.getFullYear();
  return `${dia}/${mes}/${ano}`;
}

// Encontra a hora de pico dos logins
function encontrarHoraPicoDosLogins(loginTimestamps) {
  // Ajusta todos os timestamps para o horário brasileiro (UTC-3)
  const horariosAjustados = loginTimestamps.map(login => {
    const timestamp = new Date(login.timestamp);
    timestamp.setHours(timestamp.getHours() - 3);
    return timestamp;
  });
  
  // Agrupa logins por hora
  const loginsPorHora = {};
  
  horariosAjustados.forEach(timestamp => {
    // Cria chave da hora no formato HH:00
    const chaveHora = `${String(timestamp.getHours()).padStart(2, '0')}:00`;
    if (!loginsPorHora[chaveHora]) {
      loginsPorHora[chaveHora] = 0;
    }
    loginsPorHora[chaveHora]++;
  });
  
  // Encontra a hora com mais logins
  let horaPico = '';
  let contaPico = 0;
  
  for (const [hora, contagem] of Object.entries(loginsPorHora)) {
    if (contagem > contaPico) {
      contaPico = contagem;
      horaPico = hora;
    }
  }
  
  // Formata o intervalo da hora de pico (X:XX - X+1:XX)
  if (horaPico) {
    const valorHora = parseInt(horaPico.split(':')[0], 10);
    const proximaHora = (valorHora + 1) % 24;
    const intervaloPico = `${horaPico} - ${String(proximaHora).padStart(2, '0')}:00`;
    return { intervaloPico, contaPico };
  }
  
  return { intervaloPico: 'N/A', contaPico: 0 };
}

// Filtra usuários teste (Padrão ALUNO X)
function filtrarUsuariosTeste(usuarios) {
  const filtrados = usuarios.filter(usuario => {
    // Pula usuários sem nome
    if (!usuario.name) return true;
    
    // Verifica se o nome corresponde ao padrão "ALUNO X" onde X é um número
    const padrao = /^aluno\s+\d+$/i;
    return !padrao.test(usuario.name.trim());
  });
  
  console.log(`Filtrados ${usuarios.length - filtrados.length} usuários de teste com nomes no padrão "ALUNO X"`);
  return filtrados;
}

// Gera o relatório para um mês específico
async function gerarRelatorioPorMes(mesesAtras) {
  const { dataInicial, dataFinal } = obterPeriodoDeAnalise(mesesAtras);
  
  console.log(`Gerando relatório para: ${formatarDataBR(dataInicial)} a ${formatarDataBR(dataFinal)}`);
  
  try {
    // 1. ANÁLISE DE LOGINS
    // ===================
    
    // Busca logins no período
    const logins = await LoginTimeSeriesModel.find({
      timestamp: { $gte: dataInicial, $lte: dataFinal }
    }).sort({ timestamp: -1 });
    
    console.log(`Encontrados ${logins.length} eventos de login no período`);
    
    // Encontra hora de pico dos logins
    const { intervaloPico, contaPico } = encontrarHoraPicoDosLogins(logins);
    
    // Extrai IDs únicos de usuários que fizeram login
    const idsUsuariosLogin = [...new Set(logins.map(login => 
      login.metadata && login.metadata.userId ? login.metadata.userId : null
    ).filter(id => id !== null))];
    
    console.log(`Encontrados ${idsUsuariosLogin.length} usuários únicos que fizeram login`);
    
    // 2. ANÁLISE DE ATIVIDADE DE USUÁRIOS
    // ================================
    
    // Busca atividades de questões no período
    let questoesRespondidas = await QuestionMetricModel.find({
      createdAt: { $gte: dataInicial, $lte: dataFinal }
    });
    
    console.log(`Encontradas ${questoesRespondidas.length} questões respondidas no período`);
    
    // Análise de acertos/erros nas questões
    const questoesCorretas = questoesRespondidas.filter(q => q.isCorrect).length;
    const taxaAcerto = questoesRespondidas.length > 0 
      ? (questoesCorretas / questoesRespondidas.length * 100).toFixed(2) 
      : 0;
    
    // IDs únicos de usuários que responderam questões
    const idsUsuariosQuestoes = [...new Set(questoesRespondidas.map(q => 
      q.userId ? q.userId.toString() : null
    ).filter(id => id !== null))];
    
    // Busca assistências de vídeos no período
    let videosAssistidos = await LessonMetricModel.find({
      $or: [
        { createdAt: { $gte: dataInicial, $lte: dataFinal } },
        { updatedAt: { $gte: dataInicial, $lte: dataFinal } }
      ]
    });
    
    console.log(`Encontrados ${videosAssistidos.length} registros de vídeos no período`);
    
    // IDs únicos de usuários que assistiram vídeos
    const idsUsuariosVideos = [...new Set(videosAssistidos.map(v => 
      v.userId ? v.userId.toString() : null
    ).filter(id => id !== null))];
    
    // Contagem de vídeos assistidos completamente
    const videosCompletados = videosAssistidos.filter(v => v.watched).length;
    
    // 3. OBTENÇÃO DE DADOS DE USUÁRIOS
    // ==============================
    
    // Combina todos os IDs únicos de usuários ativos
    const todosIdsUsuariosAtivos = [...new Set([
      ...idsUsuariosLogin,
      ...idsUsuariosQuestoes,
      ...idsUsuariosVideos
    ])];
    
    // Busca detalhes de todos os usuários ativos
    const usuariosAtivos = await UserModel.find({
      _id: { $in: todosIdsUsuariosAtivos }
    }).populate('schoolId');
    
    // Filtra usuários de teste
    const usuariosReais = filtrarUsuariosTeste(usuariosAtivos);

    // filter questions and videos by the usuariosReais ids
    questoesRespondidas = questoesRespondidas.filter(q => usuariosReais.some(u => u._id.toString() === q.userId.toString()));
    videosAssistidos = videosAssistidos.filter(v => usuariosReais.some(u => u._id.toString() === v.userId.toString()));
    
    // 4. Retornar dados resumidos para este mês
    return {
      data: new Date(dataInicial),
      dataFormatada: formatarDataBR(dataInicial),
      dataCurta: formatarDataCurta(dataInicial),
      totalUsuariosAtivos: usuariosReais.length,
      totalLogins: logins.length,
      horarioPico: intervaloPico,
      contagemPico: contaPico,
      totalQuestoes: questoesRespondidas.length,
      taxaAcerto: taxaAcerto,
      totalVideos: videosAssistidos.length,
      videosCompletados: videosCompletados,
      usuariosReais: usuariosReais,
      questoesRespondidas: questoesRespondidas,
      videosAssistidos: videosAssistidos,
      logins: logins
    };
    
  } catch (error) {
    console.error(`Erro ao gerar relatório para o mês ${mesesAtras} atrás:`, error);
    throw error;
  }
}

// Gera o relatório para um dia específico
async function gerarRelatorioPorDia(diasAtras) {
  const { dataInicial, dataFinal } = obterPeriodoDeAnaliseDiario(diasAtras);
  
  console.log(`Gerando relatório diário para: ${formatarDataBR(dataInicial)}`);
  
  try {
    // 1. ANÁLISE DE LOGINS
    // ===================
    
    // Busca logins no período
    const logins = await LoginTimeSeriesModel.find({
      timestamp: { $gte: dataInicial, $lte: dataFinal }
    }).sort({ timestamp: -1 });
    
    // Encontra hora de pico dos logins
    const { intervaloPico, contaPico } = encontrarHoraPicoDosLogins(logins);
    
    // Extrai IDs únicos de usuários que fizeram login
    const idsUsuariosLogin = [...new Set(logins.map(login => 
      login.metadata && login.metadata.userId ? login.metadata.userId : null
    ).filter(id => id !== null))];
    
    // 2. ANÁLISE DE ATIVIDADE DE USUÁRIOS
    // ================================
    
    // Busca atividades de questões no período
    let questoesRespondidas = await QuestionMetricModel.find({
      createdAt: { $gte: dataInicial, $lte: dataFinal }
    });
    
    // Análise de acertos/erros nas questões
    const questoesCorretas = questoesRespondidas.filter(q => q.isCorrect).length;
    const taxaAcerto = questoesRespondidas.length > 0 
      ? (questoesCorretas / questoesRespondidas.length * 100).toFixed(2) 
      : 0;
    
    // IDs únicos de usuários que responderam questões
    const idsUsuariosQuestoes = [...new Set(questoesRespondidas.map(q => 
      q.userId ? q.userId.toString() : null
    ).filter(id => id !== null))];
    
    // Busca assistências de vídeos no período
    let videosAssistidos = await LessonMetricModel.find({
      $or: [
        { createdAt: { $gte: dataInicial, $lte: dataFinal } },
        { updatedAt: { $gte: dataInicial, $lte: dataFinal } }
      ]
    });
    
    // IDs únicos de usuários que assistiram vídeos
    const idsUsuariosVideos = [...new Set(videosAssistidos.map(v => 
      v.userId ? v.userId.toString() : null
    ).filter(id => id !== null))];
    
    // Contagem de vídeos assistidos completamente
    const videosCompletados = videosAssistidos.filter(v => v.watched).length;
    
    // 3. OBTENÇÃO DE DADOS DE USUÁRIOS
    // ==============================
    
    // Combina todos os IDs únicos de usuários ativos
    const todosIdsUsuariosAtivos = [...new Set([
      ...idsUsuariosLogin,
      ...idsUsuariosQuestoes,
      ...idsUsuariosVideos
    ])];
    
    // Busca detalhes de todos os usuários ativos
    const usuariosAtivos = await UserModel.find({
      _id: { $in: todosIdsUsuariosAtivos }
    }).populate('schoolId');
    
    // Filtra usuários de teste
    const usuariosReais = filtrarUsuariosTeste(usuariosAtivos);

    // filter questions and videos by the usuariosReais ids
    questoesRespondidas = questoesRespondidas.filter(q => usuariosReais.some(u => u._id.toString() === q.userId.toString()));
    videosAssistidos = videosAssistidos.filter(v => usuariosReais.some(u => u._id.toString() === v.userId.toString()));
    
    return {
      data: new Date(dataInicial),
      dataFormatada: formatarDataBR(dataInicial),
      dataCurta: formatarDataCurta(dataInicial),
      totalUsuariosAtivos: usuariosReais.length,
      totalLogins: logins.length,
      horarioPico: intervaloPico,
      contagemPico: contaPico,
      totalQuestoes: questoesRespondidas.length,
      taxaAcerto: taxaAcerto,
      totalVideos: videosAssistidos.length,
      videosCompletados: videosCompletados,
      usuariosReais: usuariosReais,
      questoesRespondidas: questoesRespondidas,
      videosAssistidos: videosAssistidos,
      logins: logins
    };
    
  } catch (error) {
    console.error(`Erro ao gerar relatório para o dia ${diasAtras} atrás:`, error);
    throw error;
  }
}

// Gera o relatório semanal
async function gerarRelatorioSemanal(semanasAtras = 0) {
  const dataFinal = new Date();
  dataFinal.setHours(23, 59, 59, 999);
  dataFinal.setDate(dataFinal.getDate() - (semanasAtras * 7));
  
  const dataInicial = new Date(dataFinal);
  dataInicial.setDate(dataInicial.getDate() - 6);
  dataInicial.setHours(0, 0, 0, 0);
  
  // Coleta dados de cada dia da semana
  const dadosDias = [];
  for (let i = 0; i < 7; i++) {
    const dadosDia = await gerarRelatorioPorDia(semanasAtras * 7 + i);
    dadosDias.push(dadosDia);
  }
  
  // Calcula totais da semana
  const totaisSemana = {
    totalUsuariosAtivos: new Set(dadosDias.flatMap(d => d.usuariosReais.map(u => u._id.toString()))).size,
    totalLogins: dadosDias.reduce((sum, d) => sum + d.totalLogins, 0),
    totalQuestoes: dadosDias.reduce((sum, d) => sum + d.totalQuestoes, 0),
    totalVideos: dadosDias.reduce((sum, d) => sum + d.totalVideos, 0),
    videosCompletados: dadosDias.reduce((sum, d) => sum + d.videosCompletados, 0),
    taxaAcerto: dadosDias.reduce((sum, d) => sum + parseFloat(d.taxaAcerto), 0) / 7
  };
  
  return {
    dataInicial,
    dataFinal,
    dadosDias,
    totaisSemana,
    dataInicialFormatada: formatarDataCurta(dataInicial),
    dataFinalFormatada: formatarDataCurta(dataFinal)
  };
}

// Gera o relatório mensal completo
async function gerarRelatorioMensal() {
  try {
    // Coleta dados dos últimos 12 meses
    const dados = [];
    for (let i = 0; i < 12; i++) {
      const dadosMes = await gerarRelatorioPorMes(i);
      dados.push(dadosMes);
    }
    
    // Coleta dados diários do último mês
    const dadosDiarios = [];
    for (let i = 0; i < 30; i++) {
      const dadosDia = await gerarRelatorioPorDia(i);
      dadosDiarios.push(dadosDia);
    }
    
    // Coleta dados semanais do último mês
    const dadosSemanais = [];
    for (let i = 0; i < 4; i++) {
      const dadosSemana = await gerarRelatorioSemanal(i);
      dadosSemanais.push(dadosSemana);
    }
    
    // Relatório detalhado apenas para o mês mais recente
    const relatorioMesAtual = dados[0];
    
    // 4. ANÁLISE POR PERFIL DE USUÁRIO
    // ===============================
    
    // Contagem por função (role)
    const contagemPorFuncao = relatorioMesAtual.usuariosReais.reduce((acumulador, usuario) => {
      acumulador[usuario.role] = (acumulador[usuario.role] || 0) + 1;
      return acumulador;
    }, {});
    
    // Contagem por escola
    const contagemPorEscola = relatorioMesAtual.usuariosReais.reduce((acumulador, usuario) => {
      const escolaNome = usuario.schoolId ? usuario.schoolId.name : (usuario.school || 'Sem escola');
      acumulador[escolaNome] = (acumulador[escolaNome] || 0) + 1;
      return acumulador;
    }, {});
    
    // 5. CRIAÇÃO DO RELATÓRIO DETALHADO
    // ===============================
    
    // Mapeamento de usuários com suas atividades
    const relatorioUsuarios = relatorioMesAtual.usuariosReais.map(usuario => {
      // Encontra o último login deste usuário
      const loginsUsuario = relatorioMesAtual.logins.filter(
        login => login.metadata && login.metadata.userId === usuario._id.toString()
      );
      const ultimoLogin = loginsUsuario.length > 0 ? loginsUsuario[0].timestamp : null;
      
      // Conta questões respondidas por este usuário
      const questoesUsuario = relatorioMesAtual.questoesRespondidas.filter(
        q => q.userId && q.userId.toString() === usuario._id.toString()
      );
      const questoesCorretasUsuario = questoesUsuario.filter(q => q.isCorrect).length;
      
      // Conta vídeos assistidos por este usuário
      const videosUsuario = relatorioMesAtual.videosAssistidos.filter(
        v => v.userId && v.userId.toString() === usuario._id.toString()
      );
      const videosCompletadosUsuario = videosUsuario.filter(v => v.watched).length;
      return {
        nome: usuario.name,
        email: usuario.email,
        funcao: usuario.role,
        escola: usuario.schoolId ? usuario.schoolId.name : (usuario.school || 'Sem escola'),
        anoEscolar: usuario.schoolYear || 'N/A',
        ultimoLogin: ultimoLogin ? formatarDataBR(ultimoLogin) : '',
        quantidadeLogins: loginsUsuario.length,
        questoesRespondidas: questoesUsuario.length,
        questoesCorretas: questoesCorretasUsuario,
        taxaAcerto: questoesUsuario.length > 0 
          ? (questoesCorretasUsuario / questoesUsuario.length * 100).toFixed(2) + '%'
          : '0%',
        videosAssistidos: videosUsuario.length,
        videosCompletados: videosCompletadosUsuario
      };
    });
    
    // 6. SALVA O RELATÓRIO EM CSV
    // =========================
    
    // Cabeçalho do relatório completo
    const cabecalhoCompleto = [
      'Nome',
      'Escola',
      'Questões Respondidas',
      'Questões Corretas',
      'Taxa de Acerto',
      'Vídeos Assistidos',
      'Vídeos Completados'
    ].join(',');
    
    // Linhas do relatório completo
    const linhasRelatorioCompleto = relatorioUsuarios.map(u => [
      `"${u.nome}"`,
      `"${u.escola}"`,
      u.questoesRespondidas,
      u.questoesCorretas,
      `"${u.taxaAcerto}"`,
      u.videosAssistidos,
      u.videosCompletadosUsuario
    ].join(','));
    
    // Evolução nos últimos 12 meses
    const evolucaoDozeMeses = [
      '',
      'EVOLUÇÃO NOS ÚLTIMOS 12 MESES',
      'Mês,Usuários Ativos,Questões Respondidas,Taxa de Acerto,Vídeos Assistidos',
      ...dados.map(d => 
        `${d.dataCurta},${d.totalUsuariosAtivos},${d.totalQuestoes},${d.taxaAcerto}%,${d.totalVideos}`
      ),
      '',
      // Variação percentual do primeiro para o último mês
      `Variação em 12 meses,${calcularVariacaoPercentual(dados[11].totalUsuariosAtivos, dados[0].totalUsuariosAtivos)}%,${calcularVariacaoPercentual(dados[11].totalQuestoes, dados[0].totalQuestoes)}%,${calcularVariacaoPercentual(dados[11].taxaAcerto, dados[0].taxaAcerto)}%,${calcularVariacaoPercentual(dados[11].totalVideos, dados[0].totalVideos)}%`
    ];
    
    // Adiciona evolução diária do último mês
    const evolucaoDiaria = [
      '',
      'EVOLUÇÃO DIÁRIA DO ÚLTIMO MÊS',
      'Data,Usuários Ativos,Questões Respondidas,Taxa de Acerto,Vídeos Assistidos',
      ...dadosDiarios.map(d => 
        `${d.dataCurta},${d.totalUsuariosAtivos},${d.totalQuestoes},${d.taxaAcerto}%,${d.totalVideos}`
      )
    ];
    
    // Adiciona evolução semanal do último mês
    const evolucaoSemanal = [
      '',
      'EVOLUÇÃO SEMANAL DO ÚLTIMO MÊS',
      'Semana,Usuários Ativos,Questões Respondidas,Taxa de Acerto,Vídeos Assistidos',
      ...dadosSemanais.map((semana, index) => {
        return `${semana.dataInicialFormatada} a ${semana.dataFinalFormatada},${semana.totaisSemana.totalUsuariosAtivos},${semana.totaisSemana.totalQuestoes},${semana.totaisSemana.taxaAcerto.toFixed(2)}%,${semana.totaisSemana.totalVideos}`;
      })
    ];

    // Evolução nos últimos 12 meses por escola
    const evolucaoPorEscola = [
      '',
      'EVOLUÇÃO POR ESCOLA NOS ÚLTIMOS 12 MESES'
    ];

    // Agrupar dados por escola para cada mês
    const dadosPorEscolaPorMes = dados.reduce((acc, mes) => {
      // Agrupa os dados do mês por escola
      const escolasDoMes = mes.usuariosReais.reduce((escolasAcc, usuario) => {
        const escolaNome = usuario.schoolId ? usuario.schoolId.name : (usuario.school || 'Sem escola');
        
        if (!escolasAcc[escolaNome]) {
          escolasAcc[escolaNome] = {
            usuarios: 0,
            questoesRespondidas: 0,
            questoesCorretas: 0,
            videosAssistidos: 0,
            videosCompletados: 0
          };
        }
        
        // Contagem de usuários
        escolasAcc[escolaNome].usuarios++;
        
        // Questões da escola
        const questoesUsuario = mes.questoesRespondidas.filter(
          q => q.userId && q.userId.toString() === usuario._id.toString()
        );
        escolasAcc[escolaNome].questoesRespondidas += questoesUsuario.length;
        escolasAcc[escolaNome].questoesCorretas += questoesUsuario.filter(q => q.isCorrect).length;
        
        // Vídeos da escola
        const videosUsuario = mes.videosAssistidos.filter(
          v => v.userId && v.userId.toString() === usuario._id.toString()
        );
        escolasAcc[escolaNome].videosAssistidos += videosUsuario.length;
        escolasAcc[escolaNome].videosCompletados += videosUsuario.filter(v => v.watched).length;
        
        return escolasAcc;
      }, {});
      
      // Adiciona os dados do mês ao acumulador
      Object.entries(escolasDoMes).forEach(([escola, dados]) => {
        if (!acc[escola]) {
          acc[escola] = [];
        }
        acc[escola].push({
          data: mes.dataCurta,
          ...dados,
          taxaAcerto: dados.questoesRespondidas > 0 
            ? ((dados.questoesCorretas / dados.questoesRespondidas) * 100).toFixed(2)
            : 0
        });
      });
      
      return acc;
    }, {});

    // Gerar tabelas de evolução para cada escola
    Object.entries(dadosPorEscolaPorMes).forEach(([escola, mesesDados]) => {
      evolucaoPorEscola.push(
        '',
        `Escola: "${escola}"`,
        'Mês,Usuários Ativos,Questões Respondidas,Taxa de Acerto,Vídeos Assistidos,Vídeos Completados'
      );
      
      // Adiciona linha para cada mês
      mesesDados.forEach(mes => {
        evolucaoPorEscola.push(
          `${mes.data},${mes.usuarios},${mes.questoesRespondidas},${mes.taxaAcerto}%,${mes.videosAssistidos},${mes.videosCompletados}`
        );
      });
      
      // Calcula e adiciona a variação percentual
      if (mesesDados.length >= 12) {
        const primeiro = mesesDados[11]; // mês mais antigo
        const ultimo = mesesDados[0];   // mês mais recente
        
        evolucaoPorEscola.push(
          `Variação em 12 meses,${calcularVariacaoPercentual(primeiro.usuarios, ultimo.usuarios)}%,${calcularVariacaoPercentual(primeiro.questoesRespondidas, ultimo.questoesRespondidas)}%,${calcularVariacaoPercentual(primeiro.taxaAcerto, ultimo.taxaAcerto)}%,${calcularVariacaoPercentual(primeiro.videosAssistidos, ultimo.videosAssistidos)}%,${calcularVariacaoPercentual(primeiro.videosCompletados, ultimo.videosCompletados)}%`
        );
      }
    });

    // Resumo das métricas
    const resumoMetricas = [
      '',
      'RESUMO DAS MÉTRICAS',
      `Total de Usuários Ativos,${relatorioMesAtual.totalUsuariosAtivos}`,
      `Total de Logins,${relatorioMesAtual.totalLogins}`,
      `Horário de Pico de Logins,${relatorioMesAtual.horarioPico} (${relatorioMesAtual.contagemPico} logins)`,
      `Total de Questões Respondidas,${relatorioMesAtual.totalQuestoes}`,
      `Taxa Média de Acerto,${relatorioMesAtual.taxaAcerto}%`,
      `Total de Vídeos Assistidos,${relatorioMesAtual.totalVideos}`,
      `Vídeos Completados,${relatorioMesAtual.videosCompletados}`,
      '',
      'TOP 5 ESCOLAS MAIS ATIVAS',
      ...Object.entries(contagemPorEscola)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([escola, contagem]) => 
          `"${escola}",${contagem}`
        )
    ];
    
    // Adicionar estatísticas por escola
    const estatisticasPorEscola = [
      '',
      'ESTATÍSTICAS POR ESCOLA',
      'Escola,Usuários Ativos,Questões Respondidas,Taxa de Acerto,Vídeos Assistidos,Vídeos Completados'
    ];

    // Agrupar dados por escola
    const dadosPorEscola = relatorioMesAtual.usuariosReais.reduce((acc, usuario) => {
      const escolaNome = usuario.schoolId ? usuario.schoolId.name : (usuario.school || 'Sem escola');
      
      if (!acc[escolaNome]) {
        acc[escolaNome] = {
          usuarios: 0,
          questoesRespondidas: 0,
          questoesCorretas: 0,
          videosAssistidos: 0,
          videosCompletados: 0
        };
      }
      
      // Contagem de usuários
      acc[escolaNome].usuarios++;
      
      // Questões da escola
      const questoesUsuario = relatorioMesAtual.questoesRespondidas.filter(
        q => q.userId && q.userId.toString() === usuario._id.toString()
      );
      acc[escolaNome].questoesRespondidas += questoesUsuario.length;
      acc[escolaNome].questoesCorretas += questoesUsuario.filter(q => q.isCorrect).length;
      
      // Vídeos da escola
      const videosUsuario = relatorioMesAtual.videosAssistidos.filter(
        v => v.userId && v.userId.toString() === usuario._id.toString()
      );
      acc[escolaNome].videosAssistidos += videosUsuario.length;
      acc[escolaNome].videosCompletados += videosUsuario.filter(v => v.watched).length;
      
      return acc;
    }, {});

    // Adicionar linhas de estatísticas por escola
    Object.entries(dadosPorEscola).forEach(([escola, stats]) => {
      const taxaAcerto = stats.questoesRespondidas > 0 
        ? ((stats.questoesCorretas / stats.questoesRespondidas) * 100).toFixed(2)
        : 0;
        
      estatisticasPorEscola.push(
        `"${escola}",${stats.usuarios},${stats.questoesRespondidas},${taxaAcerto}%,${stats.videosAssistidos},${stats.videosCompletados}`
      );
    });

    // Adicionar totais gerais
    const totaisGerais = [
      '',
      'TOTAIS GERAIS',
      'Métrica,Valor',
      `Total de Usuários Ativos,${relatorioMesAtual.totalUsuariosAtivos}`,
      `Total de Logins,${relatorioMesAtual.totalLogins}`,
      `Total de Questões Respondidas,${relatorioMesAtual.totalQuestoes}`,
      `Taxa Média de Acerto Geral,${relatorioMesAtual.taxaAcerto}%`,
      `Total de Vídeos Assistidos,${relatorioMesAtual.totalVideos}`,
      `Total de Vídeos Completados,${relatorioMesAtual.videosCompletados}`
    ];

    // Monta o CSV completo
    const csvDados = [
      cabecalhoCompleto,
      ...linhasRelatorioCompleto,
      ...evolucaoDozeMeses,
      ...evolucaoDiaria,
      ...evolucaoSemanal,
      ...evolucaoPorEscola,
      ...resumoMetricas,
      ...estatisticasPorEscola,
      ...totaisGerais
    ].join('\n');
    
    // Gera nome do arquivo com timestamp
    const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
    const nomeArquivo = `./relatorio_mensal_${timestamp}.csv`;
    fs.writeFileSync(path.resolve(nomeArquivo), csvDados);
    
    console.log(`Relatório completo salvo em ${nomeArquivo}`);
    
    // Retorna dados consolidados para exibição no console
    return {
      dadosHoje: relatorioMesAtual,
      evolucao: dados,
      evolucaoDiaria: dadosDiarios,
      evolucaoSemanal: dadosSemanais,
      totalUsuariosAtivos: relatorioMesAtual.totalUsuariosAtivos,
      totalLogins: relatorioMesAtual.totalLogins,
      horarioPico: relatorioMesAtual.horarioPico,
      contagemPico: relatorioMesAtual.contagemPico,
      totalQuestoes: relatorioMesAtual.totalQuestoes,
      taxaAcerto: relatorioMesAtual.taxaAcerto,
      totalVideos: relatorioMesAtual.totalVideos,
      videosCompletados: relatorioMesAtual.videosCompletados,
      distribuicaoPorFuncao: contagemPorFuncao,
      escolasMaisAtivas: Object.entries(contagemPorEscola)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
    };
    
  } catch (error) {
    console.error('Erro ao gerar relatório:', error);
    throw error;
  }
}

// Calcula a variação percentual entre dois valores
function calcularVariacaoPercentual(valorAntigo, valorNovo) {
  if (valorAntigo === 0) return valorNovo > 0 ? 'Infinito' : '0';
  
  const variacao = ((valorNovo - valorAntigo) / valorAntigo) * 100;
  return variacao.toFixed(2);
}

// Execução principal
async function main() {
    await conectarAoMongoDB(CONNECTION_STRING);
    const resultado = await gerarRelatorioMensal();
    
    console.log('\n===== RESUMO DO RELATÓRIO MENSAL =====');
    console.log(`Total de usuários ativos: ${resultado.totalUsuariosAtivos}`);
    console.log(`Total de logins: ${resultado.totalLogins}`);
    console.log(`Horário de pico: ${resultado.horarioPico} (${resultado.contagemPico} logins)`);
    console.log(`Questões respondidas: ${resultado.totalQuestoes} (Taxa de acerto: ${resultado.taxaAcerto}%)`);
    console.log(`Vídeos assistidos: ${resultado.totalVideos} (Completados: ${resultado.videosCompletados})`);
    
    console.log('\nDistribuição por função:');
    Object.entries(resultado.distribuicaoPorFuncao).forEach(([funcao, contagem]) => {
      console.log(`  - ${funcao}: ${contagem} usuários`);
    });
    
    console.log('\nTop 5 escolas mais ativas:');
    resultado.escolasMaisAtivas.forEach(([escola, contagem], index) => {
      console.log(`  ${index+1}. ${escola}: ${contagem} usuários ativos`);
    });
    
    console.log('\n===== EVOLUÇÃO NOS ÚLTIMOS 12 MESES =====');
    console.log('Mês | Usuários Ativos | Questões Respondidas | Vídeos Assistidos');
    resultado.evolucao.forEach(mes => {
      console.log(`${mes.dataCurta} | ${mes.totalUsuariosAtivos.toString().padEnd(14)} | ${mes.totalQuestoes.toString().padEnd(19)} | ${mes.totalVideos}`);
    });
    
    console.log('\n===== EVOLUÇÃO DIÁRIA DO ÚLTIMO MÊS =====');
    console.log('Data | Usuários Ativos | Questões Respondidas | Vídeos Assistidos');
    resultado.evolucaoDiaria.forEach(dia => {
      console.log(`${dia.dataCurta} | ${dia.totalUsuariosAtivos.toString().padEnd(14)} | ${dia.totalQuestoes.toString().padEnd(19)} | ${dia.totalVideos}`);
    });
    
    console.log('\n===== EVOLUÇÃO SEMANAL DO ÚLTIMO MÊS =====');
    console.log('Período | Usuários Ativos | Questões Respondidas | Vídeos Assistidos');
    resultado.evolucaoSemanal.forEach(semana => {
      console.log(`${semana.dataInicialFormatada} a ${semana.dataFinalFormatada} | ${semana.totaisSemana.totalUsuariosAtivos.toString().padEnd(14)} | ${semana.totaisSemana.totalQuestoes.toString().padEnd(19)} | ${semana.totaisSemana.totalVideos}`);
    });
    
    await mongoose.disconnect();
    return true;
}

main();