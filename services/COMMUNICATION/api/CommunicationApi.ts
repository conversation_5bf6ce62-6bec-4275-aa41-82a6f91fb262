import { api } from '@/services/axios';
import type { ChannelsListResponse, PresenceUsersResponse, UnreadInfoResponse, PresenceStatus } from '../interfaces/channel';
import type { MessagesResponseBody, SendMessageJsonBody, SendMessageResponse } from '../interfaces/message';

export class CommunicationApi {
  static async listChannels(params?: { type?: 'CHAT'|'GROUP'; limit?: number; offset?: number; sortBy?: string; sortOrder?: 'asc'|'desc' }, etag?: string) {
    const res = await api.get<ChannelsListResponse>(`/communication/channels`, {
      params,
      headers: etag ? { 'If-None-Match': etag } : undefined,
      validateStatus: (s) => s === 200 || s === 304,
    });
    return res;
  }

  static async fetchMessages(channelId: string, params?: { afterOrder?: number; limit?: number; sortBy?: string; sortOrder?: 'asc'|'desc'; includeDeleted?: boolean }, etag?: string) {
    const res = await api.get<MessagesResponseBody>(`/communication/channels/${channelId}/messages`, {
      params,
      headers: etag ? { 'If-None-Match': etag } : undefined,
      validateStatus: (s) => s === 200 || s === 304,
    });
    return res;
  }

  static async markViewed(channelId: string, lastSeenOrder: number) {
    const res = await api.post(`/communication/channels/${channelId}/mark-viewed`, { lastSeenOrder });
    return res.data;
  }

  static async unreadInfo(channelId?: string, etag?: string) {
    const res = await api.get<UnreadInfoResponse>(`/communication/unread-messages`, {
      params: channelId ? { channelId } : undefined,
      headers: etag ? { 'If-None-Match': etag } : undefined,
      validateStatus: (s) => s === 200 || s === 304,
    });
    return res;
  }

  static async sendMessageJson(body: SendMessageJsonBody) {
    const res = await api.post<SendMessageResponse>(`/communication/messages`, body);
    return res.data;
  }

  static async sendMessageMultipart(form: FormData) {
    const res = await api.post<SendMessageResponse>(`/communication/messages`, form, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return res.data;
  }
}

export default CommunicationApi;

