import ApiCaller from './ApiCaller';

export interface IDoubtComment {
    id: number;
    content: string;
    userId: string;
    username: string;
    videoId: number;
    videoTitle?: string;
    parentCommentId?: number;
    doubtStatus: -1 | 0 | 1; // -1: não respondida, 0: alguém pegou, 1: respondida
    createdAt: string;
    updatedAt: string;
    video?: {
        id: number;
        title: string;
        description: string;
    };
}

export interface IDoubtsResponse {
    comments: IDoubtComment[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}

export default class DoubtsController extends ApiCaller {
    /**
     * Busca comentários/dúvidas de um grupo específico
     */
    static async getCommentsByGroup(
        groupId: string,
        options?: {
            videoId?: string;
            isDoubt?: boolean;
            page?: number;
            limit?: number;
        }
    ): Promise<IDoubtsResponse> {
        const params = new URLSearchParams();
        
        if (options?.videoId) params.append('videoId', options.videoId);
        if (options?.isDoubt !== undefined) params.append('isDoubt', String(options.isDoubt));
        if (options?.page) params.append('page', String(options.page));
        if (options?.limit) params.append('limit', String(options.limit));

        const queryString = params.toString() ? `?${params.toString()}` : '';
        
        return await this.apiCall(`/video/comments/group/${groupId}${queryString}`, {
            method: 'GET'
        });
    }

    /**
     * Busca dúvidas de múltiplos grupos (escolas) do professor
     */
    static async getDoubtsFromMultipleGroups(
        groupIds: string[],
        options?: {
            videoId?: string;
            page?: number;
            limit?: number;
        }
    ): Promise<IDoubtComment[]> {
        const allDoubts: IDoubtComment[] = [];
        
        // Busca dúvidas de cada grupo
        const promises = groupIds.map(async (groupId) => {
            try {
                const response = await this.getCommentsByGroup(groupId, {
                    ...options,
                    isDoubt: true // Força buscar apenas dúvidas
                });
                return response.comments || [];
            } catch (error) {
                console.error(`Erro ao buscar dúvidas do grupo ${groupId}:`, error);
                return [];
            }
        });

        const results = await Promise.all(promises);
        results.forEach(comments => allDoubts.push(...comments));

        // Ordena por data de criação (mais recentes primeiro)
        return allDoubts.sort((a, b) => 
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
    }
}
