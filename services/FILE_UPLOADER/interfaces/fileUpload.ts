export interface FileMetadata {
  userAgent: string;
  sourceIp: string;
  uploadMethod: string;
  uploadedAt: string;
  s3Bucket: string;
}

export interface UploadedFile {
  id: string;
  fileName: string;
  originalFileName: string;
  fileType: 'IMAGE' | 'DOCUMENT' | 'VIDEO';
  mimeType: string;
  fileSize: number;
  url: string;
  s3Key: string;
  uploadedAt: string;
  metadata: FileMetadata;
}

export interface UploadResponse {
  success: boolean;
  message: string;
  data: UploadedFile;
}

export interface UploadRequest {
  file: File;
  type: 'IMAGE' | 'DOCUMENT' | 'VIDEO';
}

export interface ValidationError {
  error: 'ValidationError';
  message: string;
  formattedMessage: string;
}

export interface AuthorizationError {
  error: 'AuthorizationError';
  message: string;
  formattedMessage: string;
}

export interface UnexpectedError {
  error: 'UnexpectedError';
  message: string;
  formattedMessage: string;
}

export type FileUploadError = ValidationError | AuthorizationError | UnexpectedError;

// Validation constants based on endpoints.md
export const FILE_TYPE_LIMITS = {
  IMAGE: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/bmp', 'image/svg+xml'],
    extensions: ['.jpeg', '.jpg', '.png', '.gif', '.webp', '.bmp', '.svg']
  },
  DOCUMENT: {
    maxSize: 50 * 1024 * 1024, // 50MB
    allowedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    extensions: ['.pdf', '.doc', '.docx']
  },
  VIDEO: {
    maxSize: 500 * 1024 * 1024, // 500MB
    allowedTypes: ['video/mp4', 'video/quicktime', 'video/x-msvideo'],
    extensions: ['.mp4', '.mov', '.avi']
  }
} as const;
