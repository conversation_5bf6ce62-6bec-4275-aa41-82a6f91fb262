import FileUploader<PERSON><PERSON>_Calling from './ApiCaller';
import { UploadRequest, UploadResponse, FILE_TYPE_LIMITS, FileUploadError } from '../interfaces';

export default class FileController {
    /**
     * Upload an image file to the file manager service
     * @param file - The image file to upload
     * @returns Promise<UploadResponse> - The upload response with file details
     */
    static async uploadImage(file: File): Promise<UploadResponse> {
        // Validate file before upload
        this.validateFile(file, 'IMAGE');
        
        try {
            const response = await FileUploaderAPI_Calling.uploadFile(
                '/fileManager/images/upload',
                file,
                'IMAGE'
            );
            
            return response as UploadResponse;
        } catch (error: any) {
            console.error('Image upload failed:', error);
            throw this.handleUploadError(error);
        }
    }

    /**
     * Upload a document file to the file manager service
     * @param file - The document file to upload
     * @returns Promise<UploadResponse> - The upload response with file details
     */
    static async uploadDocument(file: File): Promise<UploadResponse> {
        // Validate file before upload
        this.validateFile(file, 'DOCUMENT');
        
        try {
            const response = await FileUploaderAPI_Calling.uploadFile(
                '/fileManager/documents/upload',
                file,
                'DOCUMENT'
            );
            
            return response as UploadResponse;
        } catch (error: any) {
            console.error('Document upload failed:', error);
            throw this.handleUploadError(error);
        }
    }

    /**
     * Upload a video file to the file manager service
     * @param file - The video file to upload
     * @returns Promise<UploadResponse> - The upload response with file details
     */
    static async uploadVideo(file: File): Promise<UploadResponse> {
        // Validate file before upload
        this.validateFile(file, 'VIDEO');
        
        try {
            const response = await FileUploaderAPI_Calling.uploadFile(
                '/fileManager/videos/upload',
                file,
                'VIDEO'
            );
            
            return response as UploadResponse;
        } catch (error: any) {
            console.error('Video upload failed:', error);
            throw this.handleUploadError(error);
        }
    }

    /**
     * Generic upload method that determines file type automatically
     * @param file - The file to upload
     * @returns Promise<UploadResponse> - The upload response with file details
     */
    static async uploadFile(file: File): Promise<UploadResponse> {
        const fileType = this.determineFileType(file);
        
        switch (fileType) {
            case 'IMAGE':
                return this.uploadImage(file);
            case 'DOCUMENT':
                return this.uploadDocument(file);
            case 'VIDEO':
                return this.uploadVideo(file);
            default:
                throw new Error(`Unsupported file type: ${file.type}`);
        }
    }

    /**
     * Validate file against type-specific constraints
     * @param file - The file to validate
     * @param type - The expected file type
     * @throws Error if validation fails
     */
    private static validateFile(file: File, type: keyof typeof FILE_TYPE_LIMITS): void {
        const limits = FILE_TYPE_LIMITS[type];
        
        // Check file size
        if (file.size > limits.maxSize) {
            const maxSizeMB = Math.round(limits.maxSize / (1024 * 1024));
            throw new Error(`File size exceeds limit. Maximum allowed: ${maxSizeMB}MB`);
        }
        
        // Check MIME type
        if (!limits.allowedTypes.includes(file.type)) {
            throw new Error(`Invalid file type. Allowed types: ${limits.allowedTypes.join(', ')}`);
        }
        
        // Check file extension
        const extension = '.' + file.name.split('.').pop()?.toLowerCase();
        if (!limits.extensions.includes(extension)) {
            throw new Error(`Invalid file extension. Allowed extensions: ${limits.extensions.join(', ')}`);
        }
    }

    /**
     * Determine file type based on MIME type
     * @param file - The file to analyze
     * @returns The determined file type
     */
    private static determineFileType(file: File): keyof typeof FILE_TYPE_LIMITS {
        if (file.type.startsWith('image/')) {
            return 'IMAGE';
        } else if (file.type.startsWith('video/')) {
            return 'VIDEO';
        } else if (
            file.type === 'application/pdf' ||
            file.type === 'application/msword' ||
            file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ) {
            return 'DOCUMENT';
        }
        
        throw new Error(`Unable to determine file type for: ${file.type}`);
    }

    /**
     * Handle and format upload errors
     * @param error - The error from the API call
     * @returns Formatted error object
     */
    private static handleUploadError(error: any): FileUploadError {
        if (error.response) {
            const status = error.response.status;
            const data = error.response.data;
            
            switch (status) {
                case 400:
                    return {
                        error: 'ValidationError',
                        message: data.message || 'Validation failed',
                        formattedMessage: data.formattedMessage || data.message || 'Validation failed'
                    };
                case 401:
                    return {
                        error: 'AuthorizationError',
                        message: data.message || 'User authentication required',
                        formattedMessage: data.formattedMessage || 'User authentication required'
                    };
                case 500:
                default:
                    return {
                        error: 'UnexpectedError',
                        message: data.message || 'An unexpected error occurred',
                        formattedMessage: data.formattedMessage || 'An unexpected error occurred.'
                    };
            }
        }
        
        return {
            error: 'UnexpectedError',
            message: error.message || 'Network error occurred',
            formattedMessage: 'An unexpected error occurred.'
        };
    }

    /**
     * Check if a file type is supported
     * @param mimeType - The MIME type to check
     * @returns boolean indicating if the type is supported
     */
    static isFileTypeSupported(mimeType: string): boolean {
        return Object.values(FILE_TYPE_LIMITS).some(limits => 
            limits.allowedTypes.includes(mimeType)
        );
    }

    /**
     * Get file type limits for a specific type
     * @param type - The file type
     * @returns The limits for that file type
     */
    static getFileTypeLimits(type: keyof typeof FILE_TYPE_LIMITS) {
        return FILE_TYPE_LIMITS[type];
    }
}
