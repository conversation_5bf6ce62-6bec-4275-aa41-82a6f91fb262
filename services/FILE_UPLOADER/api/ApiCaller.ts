import axios, { AxiosRequestConfig } from 'axios';
import Cookies from 'js-cookie';

// Accessing environment variables with NEXT_PUBLIC_ prefix
const apiUrl: string = `${process.env.NEXT_PUBLIC_ATOMIZE_CDS_URL}`;
const apiKey: string = `${process.env.NEXT_PUBLIC_ATOMIZE_API_KEY}`;

export default class FileUploaderAPI_Calling {
    static async apiCall(endpoint: string, options: AxiosRequestConfig = {}) {
        try {
            console.log("FILE_UPLOADER API URL", apiUrl);
            console.log("FILE_UPLOADER ENDPOINT", endpoint);
            const url = `${apiUrl}${endpoint}`; // {U/R/L}{/endpoint}
            console.log("FILE_UPLOADER URL", url);
            
            const token = Cookies.get('token') || localStorage.getItem('token');
            console.log("FILE_UPLOADER TOKEN", token);
            
            const response = await axios({
                url,
                method: options.method || 'GET',
                headers: {
                    // Note: Don't set Content-Type for FormData - let browser set it with boundary
                    ...options.headers,
                    'Authorization': `Bearer ${token}`,
                },
                params: options.params,
                data: options.data
            });
            
            return response.data;
        } catch (error: any) {
            console.error("Failed to upload file:", error.response ? error.response.data : error.message);
            
            if (error.response) {
                // The request was made and the server responded with a status code
                // that falls out of the range of 2xx
                console.log(error.response.data);
                console.log(error.response.status);
                console.log(error.response.headers);
            } else if (error.request) {
                // The request was made but no response was received
                console.log(error.request);
            } else {
                // Something happened in setting up the request that triggered an Error
                console.log('Error', error.message);
            }
            console.log(error.config);
            throw error;
        }
    }

    /**
     * Upload file using multipart/form-data
     * This method specifically handles file uploads with proper Content-Type handling
     */
    static async uploadFile(endpoint: string, file: File, type: string, additionalData?: Record<string, any>) {
        try {
            const formData = new FormData();
            formData.append('file', file);
            
            // Add any additional form data
            if (additionalData) {
                Object.entries(additionalData).forEach(([key, value]) => {
                    formData.append(key, value);
                });
            }

            return await this.apiCall(endpoint, {
                method: 'POST',
                params: { TYPE: type },
                data: formData,
                headers: {
                    // Don't set Content-Type - let the browser set it automatically for FormData
                }
            });
        } catch (error) {
            console.error('File upload failed:', error);
            throw error;
        }
    }

    static async getToken(): Promise<string> {
        // Mock token, replace this with real token logic
        return "teste";
    }
}
