# FILE_UPLOADER Service Documentation

This service provides a structured way to upload files to the backend file manager, following the same pattern as other services in the CDS architecture.

## Overview

The FILE_UPLOADER service is designed to handle file uploads with proper validation, error handling, and integration with the backend file manager endpoints.

## Architecture

```
services/FILE_UPLOADER/
├── api/
│   ├── ApiCaller.ts       # HTTP client for file upload operations
│   ├── FileController.ts  # Main controller with upload methods
│   └── index.ts          # Service exports
├── interfaces/
│   ├── fileUpload.ts     # TypeScript interfaces and types
│   └── index.ts          # Interface exports
├── docs/
│   └── FileController.md # This documentation
└── index.ts              # Main service export
```

## Usage

### Basic Image Upload

```typescript
import { FileUploaderAPI } from '@/services/FILE_UPLOADER';

// Upload a single image
const handleImageUpload = async (file: File) => {
  try {
    const response = await FileUploaderAPI.fileController.uploadImage(file);
    console.log('Upload successful:', response.data.url);
    return response.data.url;
  } catch (error) {
    console.error('Upload failed:', error);
    throw error;
  }
};
```

### Generic File Upload (Auto-detect type)

```typescript
import { FileUploaderAPI } from '@/services/FILE_UPLOADER';

// Automatically detect file type and upload
const handleFileUpload = async (file: File) => {
  try {
    const response = await FileUploaderAPI.fileController.uploadFile(file);
    return response.data.url;
  } catch (error) {
    console.error('Upload failed:', error);
    throw error;
  }
};
```

### File Validation

```typescript
import { FileUploaderAPI } from '@/services/FILE_UPLOADER';

// Check if file type is supported
const isSupported = FileUploaderAPI.fileController.isFileTypeSupported('image/jpeg');

// Get file type limits
const imageLimits = FileUploaderAPI.fileController.getFileTypeLimits('IMAGE');
console.log('Max size for images:', imageLimits.maxSize);
```

## Supported File Types

### Images (TYPE=IMAGE)
- **Formats:** JPEG, JPG, PNG, GIF, WebP, BMP, SVG
- **Max size:** 10MB
- **MIME types:** `image/jpeg`, `image/png`, `image/gif`, `image/webp`, `image/bmp`, `image/svg+xml`

### Documents (TYPE=DOCUMENT) - Future Support
- **Formats:** PDF, Word, Excel
- **Max size:** 50MB
- **MIME types:** `application/pdf`, `application/msword`, etc.

### Videos (TYPE=VIDEO) - Future Support
- **Formats:** MP4, MOV, AVI
- **Max size:** 500MB
- **MIME types:** `video/mp4`, `video/quicktime`, `video/x-msvideo`

## API Methods

### FileController Methods

- `uploadImage(file: File): Promise<UploadResponse>` - Upload image files
- `uploadDocument(file: File): Promise<UploadResponse>` - Upload document files
- `uploadVideo(file: File): Promise<UploadResponse>` - Upload video files
- `uploadFile(file: File): Promise<UploadResponse>` - Auto-detect type and upload
- `isFileTypeSupported(mimeType: string): boolean` - Check if file type is supported
- `getFileTypeLimits(type: string)` - Get size and type limits for file type

## Error Handling

The service provides structured error handling with three main error types:

```typescript
// Validation Error (400)
{
  error: 'ValidationError',
  message: 'File size exceeds limit. Maximum allowed: 10MB',
  formattedMessage: 'File size exceeds limit. Maximum allowed: 10MB'
}

// Authorization Error (401)
{
  error: 'AuthorizationError',
  message: 'User authentication required',
  formattedMessage: 'User authentication required'
}

// Unexpected Error (500)
{
  error: 'UnexpectedError',
  message: 'Failed to upload file to S3',
  formattedMessage: 'An unexpected error occurred.'
}
```

## Integration Example

The service has been integrated into the question builder at `app/(interno)/acoes/upload/questoes/page.tsx`:

```typescript
// Old approach - direct API call
const uploadParaAWS = async (arquivo: File): Promise<string> => {
  const formData = new FormData();
  formData.append('file', arquivo);
  const response = await fetch('/api/upload-s3', {
    method: 'POST',
    body: formData
  });
  // ...
};

// New approach - using FILE_UPLOADER service
const uploadParaAWS = async (arquivo: File): Promise<string> => {
  const response = await FileUploaderAPI.fileController.uploadImage(arquivo);
  return response.data.url;
};
```

## Backend Integration

This service communicates with the backend endpoints documented in `endpoints.md`:

- `POST {{cds_url}}/fileManager/images/upload?TYPE=IMAGE`
- `POST {{cds_url}}/fileManager/documents/upload?TYPE=DOCUMENT` (future)
- `POST {{cds_url}}/fileManager/videos/upload?TYPE=VIDEO` (future)

## Dependencies

- `axios` for HTTP requests
- `js-cookie` for authentication token management
- Backend file manager service with endpoints as documented

## Configuration

Make sure to set the following environment variables:

```env
NEXT_PUBLIC_ATOMIZE_CDS_URL=your_backend_url
NEXT_PUBLIC_ATOMIZE_API_KEY=your_api_key
```
