groupsInterface:
  handler: src/modules/groups/handler/interface.handler
  memorySize: 4096
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action: dynamodb:*
      Resource: "*"
    - Effect: Allow
      Action: s3:*
      Resource: "*"
    - Effect: Allow
      Action: ssm:*
      Resource: "*"
    - Effect: Allow
      Action: lambda:*
      Resource: "*"
    - Effect: Allow
      Action: sqs:*
      Resource: "*"
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"
  events:
    # Basic group operations
    - http:
        path: group
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: group
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: group/byId/{groupId}
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: group/byId/{groupId}
        method: patch
        cors: true
        authorizer: authorizer
    - http:
        path: group/byId
        method: delete
        cors: true
        authorizer: authorizer
    - http:
        path: group/members/{groupId}
        method: patch
        cors: true
        authorizer: authorizer
    - http:
        path: group/members
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: group/user/groups/{userId}
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: group/nested
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: group/students/average
        method: get
        cors: true
        authorizer: authorizer

teacherGroupInterface:
  handler: src/modules/groups/handler/teacher/interface.handler
  memorySize: 4096
  maximumRetryAttempts: 0
  # iamRoleStatementsName: ${self:service}-${self:custom.stage}-ITeacherGroupManagement-lambdaRole
  iamRoleStatements:
    - Effect: Allow
      Action: dynamodb:*
      Resource: "*"
    - Effect: Allow
      Action: s3:*
      Resource: "*"
    - Effect: Allow
      Action: ssm:*
      Resource: "*"
    - Effect: Allow
      Action: lambda:*
      Resource: "*"
    - Effect: Allow
      Action: sqs:*
      Resource: "*"
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: group/teacher/groups
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: group/teacher/groups
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: group/teacher/groups/{groupId}
        method: patch
        cors: true
        authorizer: authorizer
    - http:
        path: group/teacher/groups/{groupId}
        method: delete
        cors: true
        authorizer: authorizer
    - http:
        path: group/teacher/students/manage
        method: patch
        cors: true
        authorizer: authorizer
    - http:
        path: group/teacher/students/report
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: group/teacher/students/progress
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: group/teacher/students/progress/time
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: group/teacher/students
        method: get
        cors: true
        authorizer: authorizer

coordinatorGroupInterface:
  handler: src/modules/groups/handler/coordinator/interface.handler
  memorySize: 4096
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action: dynamodb:*
      Resource: "*"
    - Effect: Allow
      Action: s3:*
      Resource: "*"
    - Effect: Allow
      Action: ssm:*
      Resource: "*"
    - Effect: Allow
      Action: lambda:*
      Resource: "*"
    - Effect: Allow
      Action: sqs:*
      Resource: "*"
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: group/coordinator/groups
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: group/coordinator/groups
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: group/coordinator/groups/{groupId}
        method: patch
        cors: true
        authorizer: authorizer
    - http:
        path: group/coordinator/groups/{groupId}
        method: delete
        cors: true
        authorizer: authorizer
    - http:
        path: group/coordinator/students/manage
        method: patch
        cors: true
        authorizer: authorizer
    - http:
        path: group/coordinator/students/report
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: group/coordinator/students/progress
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: group/coordinator/students/progress/time
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: group/coordinator/students
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: group/coordinator/schools
        method: get
        cors: true
        authorizer: authorizer

