shopInterface:
  handler: src/modules/shop/handler/shop/interface.handler
  memorySize: 4096
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action: dynamodb:*
      Resource: "*"
    - Effect: Allow
      Action: s3:*
      Resource: "*"
    - Effect: Allow
      Action: ssm:*
      Resource: "*"
    - Effect: Allow
      Action: lambda:*
      Resource: "*"
    - Effect: Allow
      Action: sqs:*
      Resource: "*"
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"
  events:
    # Shop management endpoints
    - http:
        path: shop
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: shop
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: shop/manage/items
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: shop/manage/items
        method: delete
        cors: true
        authorizer: authorizer
    - http:
        path: shop/shops
        method: get
        cors: true
        authorizer: authorizer

shopItemsInterface:
  handler: src/modules/shop/handler/items/interface.handler
  memorySize: 4096
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action: dynamodb:*
      Resource: "*"
    - Effect: Allow
      Action: s3:*
      Resource: "*"
    - Effect: Allow
      Action: ssm:*
      Resource: "*"
    - Effect: Allow
      Action: lambda:*
      Resource: "*"
    - Effect: Allow
      Action: sqs:*
      Resource: "*"
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"
  events:
    # Item endpoints
    - http:
        path: shop/items
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: shop/items
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: shop/items/update
        method: patch
        cors: true
        authorizer: authorizer
    - http:
        path: shop/items/resume
        method: get
        cors: true
        authorizer: authorizer

shopInventoryInterface:
  handler: src/modules/shop/handler/inventory/interface.handler
  memorySize: 4096
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action: dynamodb:*
      Resource: "*"
    - Effect: Allow
      Action: s3:*
      Resource: "*"
    - Effect: Allow
      Action: ssm:*
      Resource: "*"
    - Effect: Allow
      Action: lambda:*
      Resource: "*"
    - Effect: Allow
      Action: sqs:*
      Resource: "*"
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"
  events:
    # Inventory endpoints
    - http:
        path: shop/inventory
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: shop/inventory
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: shop/inventory/add-item
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: shop/inventory/buy-item
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: shop/inventory/trade
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: shop/inventory/move-item
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: shop/inventory/admin/move-item
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: shop/inventory/user-resume
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: shop/inventory/transactions-resume
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: shop/inventory/admin/items-with-status
        method: get
        cors: true
        authorizer: authorizer

shopEventHandler:
  handler: src/modules/shop/handler/events/index.handler
  memorySize: 256
  timeout: 30
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-shop-event-handler-lambda-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - sqs:ReceiveMessage
        - sqs:DeleteMessage
        - sqs:GetQueueAttributes
      Resource: "*"
  events:
    - sqs:
        arn:
          Fn::GetAtt: [shopEventQueue, Arn]
        batchSize: 10
        maximumBatchingWindow: 1