Resources:
  # S3 Bucket for Communication Images
  CommunicationImagesBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: communication-${self:custom.stage}-bucket

  # S3 Bucket Policy for Communication Images
  # CommunicationImagesBucketPolicy:
  #   Type: AWS::S3::BucketPolicy
  #   Properties:
  #     Bucket: !Ref CommunicationImagesBucket
  #     PolicyDocument:
  #       Version: '2012-10-17'
  #       Statement:
  #         - Effect: Allow
  #           Action:
  #             - 's3:GetObject'
  #           Resource:
  #             - !Join ['/', [!GetAtt [CommunicationImagesBucket, Arn], '*']]
  #           Principal: '*'



# Outputs:
#   CommunicationImagesBucketName:
#     Description: "Name of the S3 bucket for communication images"
#     Value: 
#       Ref: CommunicationImagesBucket
#     Export:
#       Name: ${self:custom.stage}-CommunicationImagesBucket
  
#   CommunicationImagesBucketArn:
#     Description: "ARN of the S3 bucket for communication images"
#     Value: 
#       Fn::GetAtt: [CommunicationImagesBucket, Arn]
#     Export:
#       Name: ${self:custom.stage}-CommunicationImagesBucketArn
