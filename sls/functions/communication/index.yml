communicationInterface:
  handler: src/modules/communication/handler/communication/interface.handler
  memorySize: 4096
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action: dynamodb:*
      Resource: "*"
    - Effect: Allow
      Action: s3:*
      Resource: "*"
    - Effect: Allow
      Action: ssm:*
      Resource: "*"
    - Effect: Allow
      Action: lambda:*
      Resource: "*"
    - Effect: Allow
      Action: sqs:*
      Resource: "*"
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"
  events:
    # Channels endpoints
    - http:
        path: communication/channels
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: communication/channels
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: communication/channels/{channelId}
        method: put
        cors: true
        authorizer: authorizer
    - http:
        path: communication/channels/{channelId}
        method: delete
        cors: true
        authorizer: authorizer
    - http:
        path: communication/channels/{channelId}/members
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: communication/channels/{channelId}/members/{memberUserId}
        method: delete
        cors: true
        authorizer: authorizer

    # Messages endpoints
    - http:
        path: communication/messages
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: communication/messages/{messageId}
        method: put
        cors: true
        authorizer: authorizer
    - http:
        path: communication/messages/{messageId}
        method: delete
        cors: true
        authorizer: authorizer
    - http:
        path: communication/channels/{channelId}/messages
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: communication/channels/{channelId}/mark-viewed
        method: post
        cors: true
        authorizer: authorizer

    # Presence and unread endpoints
    - http:
        path: communication/channels/{channelId}/presence
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: communication/unread-messages
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: communication/channels/{channelId}/unread-messages
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: communication/channels/{channelId}/online-users
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: communication/channels/{channelId}/last-seen
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: communication/channels/{channelId}/last-seen
        method: get
        cors: true
        authorizer: authorizer
