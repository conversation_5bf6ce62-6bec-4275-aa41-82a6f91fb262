fileManagerImagesInterface:
  handler: src/modules/fileManager/handler/images/interface.handler
  memorySize: 4096
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action: dynamodb:*
      Resource: "*"
    - Effect: Allow
      Action: s3:*
      Resource: "*"
    - Effect: Allow
      Action: ssm:*
      Resource: "*"
    - Effect: Allow
      Action: lambda:*
      Resource: "*"
    - Effect: Allow
      Action: sqs:*
      Resource: "*"
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: fileManager/images/upload
        method: post
        cors: true
        authorizer: authorizer