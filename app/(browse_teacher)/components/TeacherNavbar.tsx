"use client"

import Link from "next/link"
import { ArrowLeft, Home, Users, BarChart3, Bell, ChevronDown, Calendar, HelpCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import Image from "next/image"
import logobranca from "@/assets/images/logoazul.png"
import avatar from "@/assets/avatar.png"
import { useUserInformation } from "@/context/UserContext"
import { useAuth } from "@/context/AuthContext"

export default function TeacherNavbar() {
  const { user } = useUserInformation()
  const { logout } = useAuth()

  const handleLogout = () => {
    logout()
  }

  return (
    <header className="sticky top-0 z-40 border-b border-[#121c5c] bg-[linear-gradient(to_top,#040A2F,#081041)] shadow-2xl">
      <div className="px-4 sm:px-6 w-full">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Back to Student Area */}
            <Link href="/inicio">
              <Button 
                variant="outline" 
                size="sm"
                className="bg-[#081041] border-[#121c5c] text-cyan-100 hover:bg-[#101f82]/30 hover:text-cyan-100"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Área do Aluno
              </Button>
            </Link>

            {/* Logo */}
            <Link href="/professor" className="flex items-center gap-2">
              <Image
                src={logobranca}
                alt="Atomize Logo"
                className="h-8 w-8 brightness-200"
              />
              <span className="text-lg font-semibold text-white hidden md:inline">
                Atomize <span className="text-purple-300">Professor</span>
              </span>
            </Link>
          </div>

          <div className="flex items-center gap-3">
            {/* Notification Bell */}
            <Button 
              variant="ghost" 
              size="icon" 
              className="relative p-2 text-cyan-300 hover:bg-[#101f82]/30 rounded-xl transition-colors"
            >
              <Bell className="h-5 w-5" />
              <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
            </Button>

            {/* User Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button
                  className="flex items-center gap-2 p-1.5 rounded-xl bg-[#081041] hover:bg-[#101f82]/30 border border-[#121c5c] transition-colors"
                >
                  <Image
                    alt="User avatar"
                    src={avatar}
                    className="h-7 w-7 rounded-full border-2 border-[#2538b6]"
                  />
                  <div className="hidden sm:block pr-1">
                    <span className="text-sm font-medium text-cyan-100">{user?.name || 'Professor'}</span>
                  </div>
                  <ChevronDown className="h-4 w-4 text-cyan-300 hidden sm:block" />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-[#081041] border-[#121c5c] text-cyan-100">
                <DropdownMenuLabel>Área do Professor</DropdownMenuLabel>
                <DropdownMenuSeparator className="bg-[#121c5c]" />
                <DropdownMenuItem asChild>
                  <Link href="/inicio" className="flex items-center gap-2">
                    <Home className="w-4 h-4" />
                    Área do Aluno
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator className="bg-[#121c5c]" />
                <DropdownMenuItem onClick={handleLogout} className="text-red-400 hover:bg-[#101f82] focus:bg-[#101f82] transition-colors">
                  Sair
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  )
} 