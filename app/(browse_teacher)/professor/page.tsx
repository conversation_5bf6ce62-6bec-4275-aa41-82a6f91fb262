"use client"

import React, { useState, useEffect } from 'react'
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { MapPin, Users, BookOpen, BarChart2, Plus, HelpCircle, GraduationCap, Calendar, MessageSquare, Star, ChevronRight, Award, ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { CosmicTabs, CosmicTabContent } from './components/CosmicTabs'
import { TeacherDoubts } from './components/TeacherDoubts'
import TeacherManagerController from '@/services/CDS/api/TeacherManagerController'
import { ITeacherGroup } from '@/services/CDS/interfaces/teacherManager'
import { Analytics } from './components/Analytics'
import { GroupsManager } from './components/GroupsManager'
import { GroupDetails } from './components/GroupDetails'
import { ProfessorStats } from './components/ProfessorStats'
import { AnimatePresence, motion } from 'framer-motion'
import TeacherCalendar from './components/TeacherCalendar'

export default function ProfessorPage() {
  const [activeTab, setActiveTab] = useState("groups")
  const [groups, setGroups] = useState<ITeacherGroup[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedGroupId, setSelectedGroupId] = useState<string | null>(null)
  const [teacherStats, setTeacherStats] = useState<{
    totalGroups: number;
    totalStudents: number;
    avgEngagement: number;
    loading: boolean;
  }>({
    totalGroups: 0,
    totalStudents: 0,
    avgEngagement: 0,
    loading: true
  })
  const router = useRouter()

  const tabs = [
    { value: "groups", label: "Grupos" },
    { value: "calendar", label: "Calendário" },
    { value: "doubts", label: "Dúvidas" }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.1 }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.3 }
    }
  }

  const fetchGroups = async () => {
    try {
      const response = await TeacherManagerController.getTeacherGroups('current')
      setGroups(response.groups)
      
      // Calculate real statistics
      const totalStudents = response.groups.reduce((acc, group) => 
        acc + group.memberClassification.students.length, 0
      )
      
      // Calculate engagement based on group progress data
      let totalEngagement = 0
      let validGroupsForEngagement = 0
      
      try {
        // Get group IDs for performance calculation
        const groupIds = response.groups.map(group => group.id).filter(Boolean)
        
        if (groupIds.length > 0) {
          const progressData = await TeacherManagerController.getGroupProgress(groupIds, 'MONTH', 3)
          
          if (progressData && progressData.length > 0) {
            progressData.forEach(groupProgress => {
              if (groupProgress.periods && groupProgress.periods.length > 0) {
                // Calculate engagement based on recent periods
                const recentPeriods = groupProgress.periods.slice(-2) // Last 2 periods
                
                recentPeriods.forEach(period => {
                  if (period.metrics) {
                    const completionRate = period.metrics.questionMetrics.completionRate || 0
                    const activeStudents = period.metrics.engagement.activeStudents || 0
                    const totalStudentsInGroup = period.students?.length || 1
                    
                    // Calculate engagement as a combination of completion rate and active student percentage
                    const studentEngagementRate = totalStudentsInGroup > 0 ? (activeStudents / totalStudentsInGroup) * 100 : 0
                    const combinedEngagement = (completionRate + studentEngagementRate) / 2
                    
                    totalEngagement += combinedEngagement
                    validGroupsForEngagement++
                  }
                })
              }
            })
          }
        }
      } catch (engagementError) {
        console.log('Could not calculate engagement metrics:', engagementError)
        // Fallback: calculate simple engagement based on active groups
        validGroupsForEngagement = response.groups.filter(g => g.memberClassification.students.length > 0).length
        totalEngagement = validGroupsForEngagement * 75 // Assume 75% base engagement for active groups
      }
      
      const avgEngagement = validGroupsForEngagement > 0 ? 
        Math.round(totalEngagement / validGroupsForEngagement) : 0
      
      setTeacherStats({
        totalGroups: response.groups.length,
        totalStudents,
        avgEngagement: Math.min(100, Math.max(0, avgEngagement)), // Clamp between 0-100
        loading: false
      })
      
    } catch (error) {
      console.error('Error fetching groups:', error)
      setTeacherStats(prev => ({ ...prev, loading: false }))
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchGroups()
  }, [])

  const handleGroupSelect = (groupId: string) => {
    setSelectedGroupId(groupId)
  }

  const handleGroupClose = () => {
    setSelectedGroupId(null)
  }

  const getTotalStudents = () => {
    return teacherStats.totalStudents
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#040A2F] to-[#0F2057] text-blue-100 py-8 px-4 sm:px-6 lg:px-8">
      <motion.div 
        className="max-w-[1400px] mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Header Section */}
        <motion.div 
          className="flex flex-col gap-6 mb-8"
          variants={itemVariants}
        >
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <div>
                <motion.h1
                  className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500"
                  initial={{ y: -20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                >
                  Painel do Professor
                </motion.h1>
                <p className="text-blue-300 text-lg mt-2">Gerencie suas turmas e acompanhe o progresso dos alunos</p>
              </div>
            </div>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 px-4 py-2 rounded-lg"
            >
              <GraduationCap className="h-5 w-5" />
              <span className="font-medium">Professor</span>
            </motion.div>
          </div>
          
          {/* Enhanced Stats Section */}
          <motion.div
            variants={itemVariants}
            className="grid grid-cols-1 md:grid-cols-3 gap-6"
          >
            <Card className="bg-gradient-to-r from-blue-900/30 to-blue-800/30 border-blue-700/50 backdrop-blur-sm p-6 hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-300 text-sm font-medium">Total de Grupos</p>
                  <p className="text-3xl font-bold text-blue-100 mt-1">
                    {teacherStats.loading ? (
                      <div className="animate-pulse bg-blue-600/30 h-8 w-12 rounded"></div>
                    ) : (
                      teacherStats.totalGroups
                    )}
                  </p>
                  <p className="text-blue-400 text-xs mt-1">Turmas ativas</p>
                </div>
                <div className="bg-blue-500/20 p-3 rounded-xl">
                  <Users className="h-8 w-8 text-blue-300" />
                </div>
              </div>
            </Card>

            <Card className="bg-gradient-to-r from-purple-900/30 to-purple-800/30 border-purple-700/50 backdrop-blur-sm p-6 hover:shadow-lg hover:shadow-purple-500/20 transition-all duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-300 text-sm font-medium">Total de Alunos</p>
                  <p className="text-3xl font-bold text-purple-100 mt-1">
                    {teacherStats.loading ? (
                      <div className="animate-pulse bg-purple-600/30 h-8 w-12 rounded"></div>
                    ) : (
                      teacherStats.totalStudents
                    )}
                  </p>
                  <p className="text-purple-400 text-xs mt-1">Estudantes ativos</p>
                </div>
                <div className="bg-purple-500/20 p-3 rounded-xl">
                  <BookOpen className="h-8 w-8 text-purple-300" />
                </div>
              </div>
            </Card>

            <Card className="bg-gradient-to-r from-green-900/30 to-green-800/30 border-green-700/50 backdrop-blur-sm p-6 hover:shadow-lg hover:shadow-green-500/20 transition-all duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-300 text-sm font-medium">Engajamento</p>
                  <p className="text-3xl font-bold text-green-100 mt-1">
                    {teacherStats.loading ? (
                      <div className="animate-pulse bg-green-600/30 h-8 w-12 rounded"></div>
                    ) : (
                      `${teacherStats.avgEngagement}%`
                    )}
                  </p>
                  <p className="text-green-400 text-xs mt-1">
                    {teacherStats.loading ? 'Calculando...' : 'Participação média'}
                  </p>
                </div>
                <div className="bg-green-500/20 p-3 rounded-xl">
                  <Award className="h-8 w-8 text-green-300" />
                </div>
              </div>
            </Card>
          </motion.div>        
        </motion.div>

        {/* Tabs Navigation */}
        <motion.div variants={itemVariants}>
          <CosmicTabs 
            tabs={tabs}
            value={activeTab} 
            onValueChange={setActiveTab}
          />
        </motion.div>

        {/* Tab Content */}
        <motion.div 
          className="mt-8" 
          style={{ minHeight: 400 }}
          variants={itemVariants}
        >
          <AnimatePresence mode="wait">
            {activeTab === "groups" && (
              <motion.div
                key="groups"
                data-tab-content="groups"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
              >
                <GroupsManager 
                  groups={groups} 
                  onGroupSelect={handleGroupSelect}
                  onGroupsUpdate={fetchGroups}
                />
                {selectedGroupId && (
                  <GroupDetails
                    groupId={selectedGroupId}
                    onGroupUpdate={fetchGroups}
                    onClose={handleGroupClose}
                  />
                )}
              </motion.div>
            )}
            {activeTab === "calendar" && (
              <motion.div
                key="calendar"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
              >
                <TeacherCalendar />
              </motion.div>
            )}
            {activeTab === "doubts" && (
              <motion.div
                key="doubts"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
              >
                <TeacherDoubts />
              </motion.div>
            )}
            {activeTab === "analytics" && (
              <motion.div
                key="analytics"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
              >
                <Analytics teacherId="current" />
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </motion.div>
    </div>
  )
}
