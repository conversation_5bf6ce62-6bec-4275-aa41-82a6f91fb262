"use client"

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { 
  Users, 
  School, 
  BookOpen, 
  BarChart3, 
  Star, 
  ArrowUpRight, 
  ChevronRight,
  Eye,
  GraduationCap,
  Target,
  TrendingUp,
  Calendar,
  Award,
  Globe
} from 'lucide-react'
import { ITeacherGroup } from '@/services/CDS/interfaces/teacherManager'

interface GroupsManagerProps {
  groups: ITeacherGroup[];
  onGroupSelect: (groupId: string) => void;
  onGroupsUpdate: () => void;
}

export const GroupsManager: React.FC<GroupsManagerProps> = ({
  groups,
  onGroupSelect,
  onGroupsUpdate
}) => {
  const [selectedGroupId, setSelectedGroupId] = useState<string | null>(null)

  const getGroupId = (group: ITeacherGroup): string => {
    return group.id;
  }

  const getGroupIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'school':
        return <School className="w-6 h-6 text-blue-400" />;
      case 'class':
        return <BookOpen className="w-6 h-6 text-purple-400" />;
      case 'school_year':
        return <GraduationCap className="w-6 h-6 text-green-400" />;
      default:
        return <Users className="w-6 h-6 text-cyan-400" />;
    }
  }

  const formatCategory = (category: string): string => {
    switch (category.toLowerCase()) {
      case 'school':
        return 'Escola';
      case 'class':
        return 'Turma';
      case 'school_year':
        return 'Ano Letivo';
      case 'generic':
        return 'Genérico';
      default:
        return category;
    }
  }

  const getCategoryGradient = (category: string): string => {
    switch (category.toLowerCase()) {
      case 'school':
        return 'from-blue-900/40 to-blue-800/30';
      case 'class':
        return 'from-purple-900/40 to-purple-800/30';
      case 'school_year':
        return 'from-green-900/40 to-green-800/30';
      default:
        return 'from-cyan-900/40 to-cyan-800/30';
    }
  }

  const getBorderColor = (category: string): string => {
    switch (category.toLowerCase()) {
      case 'school':
        return 'border-blue-500/30';
      case 'class':
        return 'border-purple-500/30';
      case 'school_year':
        return 'border-green-500/30';
      default:
        return 'border-cyan-500/30';
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.1 }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.3 }
    }
  }

  if (groups.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center py-16"
      >
        <div className="bg-gradient-to-r from-blue-900/20 to-purple-900/20 rounded-xl p-12 border border-blue-500/20 backdrop-blur-sm">
          <motion.div
            animate={{ 
              rotate: [0, 10, -10, 0],
              scale: [1, 1.05, 1]
            }}
            transition={{ 
              duration: 3, 
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="w-24 h-24 mx-auto mb-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center"
          >
            <Users className="w-12 h-12 text-white" />
          </motion.div>
          <h3 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-300 mb-4">
            Nenhum Grupo Encontrado
          </h3>
          <p className="text-blue-400 max-w-md mx-auto">
            Você ainda não possui grupos de estudantes. Entre em contato com a administração para criar seus primeiros grupos.
          </p>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Header */}
      <motion.div variants={itemVariants} className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-300">
            Seus Grupos
          </h2>
          <p className="text-blue-400 text-sm mt-1">
            Gerencie e acompanhe o progresso dos seus {groups.length} grupos
          </p>
        </div>
        <div className="flex items-center gap-2 bg-gradient-to-r from-blue-600/20 to-purple-600/20 px-4 py-2 rounded-xl border border-blue-500/30">
          <Target className="h-4 w-4 text-blue-300" />
          <span className="text-blue-100 font-medium">{groups.length} Grupos Ativos</span>
        </div>
      </motion.div>

      {/* Groups Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {groups.map((group, index) => {
          const totalStudents = group.memberClassification.students.length
          const totalMembers = Object.values(group.memberClassification).reduce(
            (acc: number, members: any[]) => acc + members.length, 0
          )
          
          return (
            <motion.div
              key={getGroupId(group)}
              variants={itemVariants}
              whileHover={{ 
                scale: 1.02,
                y: -5
              }}
              whileTap={{ scale: 0.98 }}
              className="group cursor-pointer"
              onClick={() => onGroupSelect(getGroupId(group))}
            >
              <Card className={`
                relative overflow-hidden bg-gradient-to-b ${getCategoryGradient(group.category)} 
                ${getBorderColor(group.category)} backdrop-blur-sm p-6 h-full
                hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300
                group-hover:border-opacity-60
              `}>
                {/* Glow Effect */}
                <div className="pointer-events-none absolute -top-24 -right-20 h-[200px] w-[200px] rounded-full bg-blue-600/10 blur-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className={`
                      p-3 rounded-xl bg-gradient-to-r 
                      ${group.category.toLowerCase() === 'school' ? 'from-blue-600 to-blue-700' : ''}
                      ${group.category.toLowerCase() === 'class' ? 'from-purple-600 to-purple-700' : ''}
                      ${group.category.toLowerCase() === 'school_year' ? 'from-green-600 to-green-700' : ''}
                      ${!['school', 'class', 'school_year'].includes(group.category.toLowerCase()) ? 'from-cyan-600 to-cyan-700' : ''}
                      shadow-lg
                    `}>
                      {getGroupIcon(group.category)}
                    </div>
                    <div>
                      <span className="text-xs font-medium text-blue-400 uppercase tracking-wide">
                        {formatCategory(group.category)}
                      </span>
                    </div>
                  </div>
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    className="opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  >
                    <ArrowUpRight className="w-5 h-5 text-blue-300" />
                  </motion.div>
                </div>

                {/* Group Info */}
                <div className="mb-6">
                  <h3 className="text-xl font-bold text-blue-100 mb-2 line-clamp-2 min-h-[3.5rem]">
                    {group.name}
                  </h3>
                  <p className="text-blue-300 text-sm line-clamp-3 min-h-[3rem]">
                    {group.description || 'Grupo de estudantes para acompanhamento de progresso e atividades.'}
                  </p>
                </div>

                {/* Stats */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4 text-blue-400" />
                      <span className="text-sm text-blue-300">Estudantes</span>
                    </div>
                    <span className="text-lg font-bold text-blue-100">{totalStudents}</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Globe className="w-4 h-4 text-purple-400" />
                      <span className="text-sm text-purple-300">Total de Membros</span>
                    </div>
                    <span className="text-lg font-bold text-purple-100">{totalMembers}</span>
                  </div>

                  {/* Progress Bar */}
                  <div className="mt-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-xs text-blue-400">Atividade do Grupo</span>
                      <span className="text-xs text-blue-300">75%</span>
                    </div>
                    <div className="w-full bg-blue-900/30 rounded-full h-2">
                      <motion.div 
                        className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: "75%" }}
                        transition={{ duration: 1, delay: index * 0.1 }}
                      />
                    </div>
                  </div>
                </div>

                {/* Action Button */}
                <div className="mt-6 pt-4 border-t border-blue-500/20">
                  <Button 
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 group-hover:shadow-lg transition-all duration-300"
                    size="sm"
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    Ver Detalhes
                    <ChevronRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                  </Button>
                </div>
              </Card>
            </motion.div>
          )
        })}
      </div>
    </motion.div>
  )
}
