'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Calendar, ChevronLeft, ChevronRight, Clock, AlertCircle, Eye, Plus, X, Loader2, CheckCircle, AlertTriangle, Info, Bookmark, Users } from 'lucide-react'
import CalendarAPI from '@/services/CALENDAR/api'
import { ICalendarEvent, ICreateCalendarEventRequest } from '@/services/CALENDAR/interfaces'
import { 
  formatEventDate, 
  isEventToday, 
  isEventInFuture, 
  isEventOverdue,
  getEventTypeDisplayName,
  getContentTypeDisplayName,
  sortEventsByDate
} from '@/services/CALENDAR/utils/calendarUtils'
import { useAuth } from '@/context/AuthContext'
import TeacherManagerController from '@/services/CDS/api/TeacherManagerController'
import { ITeacherGroup } from '@/services/CDS/interfaces/teacherManager'

const TeacherCalendar: React.FC = () => {
  const { authData } = useAuth()
  const [currentDate, setCurrentDate] = useState(new Date())
  const [events, setEvents] = useState<ICalendarEvent[]>([])
  const [groups, setGroups] = useState<ITeacherGroup[]>([])
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isGroupsLoading, setIsGroupsLoading] = useState(true)
  const [isAddingEvent, setIsAddingEvent] = useState(false)
  const [isUpdatingEvent, setIsUpdatingEvent] = useState(false)
  const [dayActionDate, setDayActionDate] = useState<Date | null>(null)
  const [showDayEvents, setShowDayEvents] = useState(false)
  const [viewingEvent, setViewingEvent] = useState<ICalendarEvent | null>(null)
  
  // New event form state for teachers
  const [newEvent, setNewEvent] = useState<Partial<ICreateCalendarEventRequest>>({
    type: 'TASK',
    contentData: {
      title: '',
      description: '',
      type: 'VIDEO'
    },
    startDate: '',
    endDate: '',
    isContinuous: false,
    groupId: ''
  })

  const daysInMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate()
  const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1).getDay()

  // Fetch teacher groups
  const fetchGroups = useCallback(async () => {
    if (!authData) return
    
    setIsGroupsLoading(true)
    try {
      const response = await TeacherManagerController.getTeacherGroups('current')
      setGroups(response.groups)
    } catch (error) {
      console.error('Failed to fetch teacher groups:', error)
      setGroups([])
    } finally {
      setIsGroupsLoading(false)
    }
  }, [authData])

  // Fetch all events (global + user events) from API
  const fetchEvents = useCallback(async () => {
    if (!authData) return
    
    setIsLoading(true)
    try {
      const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
      const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)
      
      console.log('Fetching events for:', {
        startDate: startOfMonth.toISOString(),
        endDate: endOfMonth.toISOString()
      })
      
      const response = await CalendarAPI.getAllCalendarEvents({
        startDate: startOfMonth.toISOString(),
        endDate: endOfMonth.toISOString()
      })
      
      console.log('API Response:', response)
      
      // Handle different response structures
      let eventsData: ICalendarEvent[] = []
      if (Array.isArray(response)) {
        eventsData = response
      } else if (response.data && Array.isArray(response.data)) {
        eventsData = response.data
      } else if (response.success && Array.isArray(response.data)) {
        eventsData = response.data
      } else {
        console.error('Unexpected API response structure:', response)
        eventsData = []
      }
      
      console.log('Events data to set:', eventsData)
      setEvents(sortEventsByDate(eventsData))
    } catch (error) {
      console.error('Failed to fetch events:', error)
      setEvents([])
    } finally {
      setIsLoading(false)
    }
  }, [currentDate, authData])

  useEffect(() => {
    fetchGroups()
    fetchEvents()
  }, [fetchGroups, fetchEvents])

  const prevMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1))
  }

  const nextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1))
  }

  const prevYear = () => {
    setCurrentDate(new Date(currentDate.getFullYear() - 1, currentDate.getMonth(), 1))
  }

  const nextYear = () => {
    setCurrentDate(new Date(currentDate.getFullYear() + 1, currentDate.getMonth(), 1))
  }

  const addEvent = async () => {
    if (!newEvent.contentData?.title?.trim() || 
        !newEvent.contentData?.description?.trim() || 
        !newEvent.startDate ||
        (newEvent.isContinuous && !newEvent.endDate)) {
      return
    }
    
    setIsUpdatingEvent(true)
    try {
      // Use the startDate from the form directly
      const startDateTime = new Date(newEvent.startDate)
      
      // For end date, use the endDate if continuous, otherwise same as start
      let endDateTime = startDateTime
      if (newEvent.isContinuous && newEvent.endDate) {
        endDateTime = new Date(newEvent.endDate)
      }

      const eventData: ICreateCalendarEventRequest = {
        type: newEvent.type! || 'INFO',
        contentData: {
          title: newEvent.contentData.title.trim(),
          description: newEvent.contentData.description.trim(),
          resourceId: 'null',
          type: newEvent.contentData.type!
        },
        startDate: startDateTime.toISOString(),
        endDate: endDateTime.toISOString(),
        isContinuous: newEvent.isContinuous || false,
        isGlobal: false, // Professores não podem criar eventos globais
        groupId: newEvent.groupId || undefined,
        ownerId: authData?._id || '',
        ownerUsername: authData?.username || ''
      }

      console.log('Creating teacher event:', eventData)
      await CalendarAPI.createCalendarEvent(eventData)
      await fetchEvents()
      
      // Reset form
      setNewEvent({
        type: 'TASK',
        contentData: {
          title: '',
          description: '',
          type: 'VIDEO'
        },
        startDate: '',
        endDate: '',
        isContinuous: false,
        groupId: ''
      })
      setIsAddingEvent(false)
      setSelectedDate(null)
    } catch (error) {
      console.error('Failed to create event:', error)
    } finally {
      setIsUpdatingEvent(false)
    }
  }

  const removeEvent = async (eventId: string) => {
    try {
      await CalendarAPI.deleteCalendarEvent(eventId)
      await fetchEvents()
    } catch (error) {
      console.error('Failed to delete event:', error)
    }
  }

  const updateEventStatus = async (eventId: string, updates: {
    isCompleted?: boolean
    isBookmarked?: boolean
    isSetToInvisible?: boolean
  }) => {
    try {
      await CalendarAPI.updateUserEventStats(eventId, updates)
      await fetchEvents()
    } catch (error) {
      console.error('Failed to update event status:', error)
    }
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate)
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1)
    } else {
      newDate.setMonth(newDate.getMonth() + 1)
    }
    setCurrentDate(newDate)
  }

  const getEventsForDate = (day: number) => {
    if (!day) return []
    
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day)
    return events.filter(event => {
      const eventDate = new Date(event.startDate)
      const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
      
      // Check if event starts on this date
      const startsOnDate = eventDate.toDateString() === targetDate.toDateString()
      
      // Check if event is continuous and spans this date
      const isContinuous = event.isContinuous && event.endDate
      const spansDate = isContinuous && eventDate <= targetDate && event.endDate && new Date(event.endDate) >= targetDate
      
      return startsOnDate || spansDate
    })
  }

  const isDateInRange = (date: Date) => {
    return events.some(event => {
      if (!event.isContinuous || !event.endDate) return false
      const eventStart = new Date(event.startDate)
      const eventEnd = new Date(event.endDate)
      return eventStart <= date && eventEnd >= date
    })
  }

  const getEventIcon = (event: ICalendarEvent) => {
    switch (event.type) {
      case 'TASK':
        return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'ALERT':
        return <AlertTriangle className="w-4 h-4 text-red-400" />
      case 'INFO':
        return <Info className="w-4 h-4 text-blue-400" />
      default:
        return <Calendar className="w-4 h-4 text-yellow-400" />
    }
  }

  const getEventStatusColor = (event: ICalendarEvent) => {
    if (event.userStats?.isCompleted) return 'text-green-400'
    if (isEventOverdue(event)) return 'text-red-400'
    if (isEventToday(event)) return 'text-yellow-400'
    if (isEventInFuture(event)) return 'text-blue-400'
    return 'text-gray-400'
  }

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null)
    }
    
    // Add all days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day)
    }
    
    return days
  }

  const handleViewDayEvents = (date: Date) => {
    setSelectedDate(date)
    setShowDayEvents(true)
    setDayActionDate(null)
  }

  const handleAddEventForDay = (date: Date) => {
    setSelectedDate(date)
    setDayActionDate(null)
    
    // Set default time to 9:00 AM for the selected date
    const dateTime = new Date(date)
    dateTime.setHours(9, 0, 0, 0)
    const timeString = dateTime.toTimeString().slice(0, 5) // Get HH:MM format
    
    setNewEvent(prev => ({
      ...prev,
      startDate: `${date.toISOString().split('T')[0]}T${timeString}`
    }))
    setIsAddingEvent(true)
  }

  const handleCloseDayActions = () => {
    setDayActionDate(null)
  }

  const getGroupName = (groupId: string) => {
    const group = groups.find(g => g.id === groupId)
    return group ? group.name : 'Grupo não encontrado'
  }

  const monthNames = [
    'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ]

  const weekDays = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']

  const todayEvents = events.filter(isEventToday)
  const upcomingEvents = events.filter(isEventInFuture).slice(0, 5)
  const overdueEvents = events.filter(isEventOverdue)

  const days = getDaysInMonth(currentDate)

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="flex items-center justify-between"
      >
        <div>
          <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-300">
            Calendário
          </h2>
          <p className="text-blue-400 text-sm mt-1">
            Visualize e gerencie eventos e atividades programadas
          </p>
        </div>
        <Button
          onClick={() => setIsAddingEvent(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          <Plus className="w-4 h-4 mr-2" />
          Novo Evento
        </Button>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Calendar */}
        <div className="lg:col-span-2">
          <Card className="bg-gradient-to-r from-[#0A1340]/80 to-[#1A237E]/80 border-blue-700/50 p-6 backdrop-blur-sm">
            {/* Calendar Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <h3 className="text-xl font-semibold text-blue-100">
                  {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
                </h3>
              </div>
              <div className="flex items-center gap-2">
                {/* Year Navigation */}
                <div className="flex gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={prevYear}
                    className="border-blue-600/50 text-blue-300 hover:bg-blue-600/20"
                    title="Ano anterior"
                  >
                    ⟨⟨
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={nextYear}
                    className="border-blue-600/50 text-blue-300 hover:bg-blue-600/20"
                    title="Próximo ano"
                  >
                    ⟩⟩
                  </Button>
                </div>
                {/* Month Navigation */}
                <div className="flex gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={prevMonth}
                    className="border-blue-600/50 text-blue-300 hover:bg-blue-600/20"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={nextMonth}
                    className="border-blue-600/50 text-blue-300 hover:bg-blue-600/20"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Calendar Grid */}
            {isLoading ? (
              <div className="flex items-center justify-center py-20">
                <Loader2 className="animate-spin h-8 w-8 text-blue-400" />
              </div>
            ) : (
              <div className="grid grid-cols-7 gap-2">
                {/* Week days */}
                {weekDays.map(day => (
                  <div key={day} className="text-center text-sm font-medium text-blue-400 py-2">
                    {day}
                  </div>
                ))}
                
                {/* Empty cells for days before the first day of the month */}
                {Array.from({ length: firstDayOfMonth }).map((_, index) => (
                  <div key={`empty-${index}`} className="h-24 sm:h-32" />
                ))}
                
                {/* Calendar days */}
                {Array.from({ length: daysInMonth }, (_, index) => {
                  const day = index + 1
                  const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day)
                  const dayEvents = getEventsForDate(day)
                  const isToday = date.toDateString() === new Date().toDateString()
                  const hasEvents = dayEvents.length > 0
                  const isInRange = isDateInRange(date)

                  return (
                    <motion.div
                      key={day}
                      whileHover={{ scale: 1.05 }}
                      className={`
                        relative h-24 sm:h-32 border border-blue-700/30 rounded-lg p-2 cursor-pointer
                        transition-all duration-200 hover:border-blue-500/50 hover:bg-blue-900/30
                        ${isToday ? 'bg-blue-800/40 border-blue-400' : 'bg-blue-900/20'}
                        ${hasEvents ? 'border-yellow-400/50' : ''}
                        ${isInRange ? 'bg-purple-900/20 border-purple-400/50' : ''}
                      `}
                      onClick={() => setDayActionDate(date)}
                    >
                      <div className={`text-sm font-medium mb-1 ${isToday ? 'text-blue-200' : 'text-gray-300'}`}>
                        {day}
                      </div>
                      
                      {dayEvents.length > 0 && (
                        <div className="space-y-1">
                          {dayEvents.slice(0, 2).map((event, idx) => (
                            <div
                              key={event._id}
                              className={`text-xs p-1 rounded truncate ${
                                event.isGlobal 
                                  ? 'bg-purple-500/30 text-purple-200' 
                                  : event.groupId 
                                    ? 'bg-green-500/30 text-green-200'
                                    : 'bg-blue-500/30 text-blue-200'
                              }`}
                              onClick={(e) => {
                                e.stopPropagation()
                                setViewingEvent(event)
                              }}
                            >
                              {event.contentData.title}
                            </div>
                          ))}
                          {dayEvents.length > 2 && (
                            <div className="text-xs text-gray-400">
                              +{dayEvents.length - 2} mais
                            </div>
                          )}
                        </div>
                      )}
                      
                      {hasEvents && (
                        <div className="absolute bottom-1 right-1">
                          <div className="w-2 h-2 bg-yellow-400 rounded-full" />
                        </div>
                      )}
                    </motion.div>
                  )
                })}
              </div>
            )}
          </Card>
        </div>

        {/* Sidebar with events */}
        <div className="space-y-4">
          {/* Today's Events */}
          <Card className="bg-gradient-to-r from-[#0A1340]/80 to-[#1A237E]/80 border-blue-700/50 p-4 backdrop-blur-sm">
            <h4 className="text-lg font-semibold text-blue-100 mb-3 flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Hoje
            </h4>
            {isLoading ? (
              <div className="space-y-2">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse bg-blue-600/30 h-12 rounded"></div>
                ))}
              </div>
            ) : todayEvents.length > 0 ? (
              <div className="space-y-2">
                {todayEvents.map(event => (
                  <div key={event._id} className="bg-[#061037]/50 p-3 rounded-lg border border-blue-800/30">
                    <p className="text-sm font-medium text-blue-100">{event.contentData.title}</p>
                    <p className="text-xs text-blue-400 mt-1">{getEventTypeDisplayName(event.type)}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-blue-400 text-sm">Nenhum evento hoje</p>
            )}
          </Card>

          {/* Upcoming Events */}
          <Card className="bg-gradient-to-r from-[#0A1340]/80 to-[#1A237E]/80 border-blue-700/50 p-4 backdrop-blur-sm">
            <h4 className="text-lg font-semibold text-blue-100 mb-3 flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Próximos
            </h4>
            {isLoading ? (
              <div className="space-y-2">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse bg-blue-600/30 h-12 rounded"></div>
                ))}
              </div>
            ) : upcomingEvents.length > 0 ? (
              <div className="space-y-2">
                {upcomingEvents.map(event => (
                  <div key={event._id} className="bg-[#061037]/50 p-3 rounded-lg border border-blue-800/30">
                    <p className="text-sm font-medium text-blue-100">{event.contentData.title}</p>
                    <p className="text-xs text-blue-400 mt-1">{formatEventDate(event.startDate)}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-blue-400 text-sm">Nenhum evento próximo</p>
            )}
          </Card>

          {/* Overdue Events */}
          {overdueEvents.length > 0 && (
            <Card className="bg-gradient-to-r from-red-900/30 to-red-800/30 border-red-600/50 p-4 backdrop-blur-sm">
              <h4 className="text-lg font-semibold text-red-100 mb-3 flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                Atrasados
              </h4>
              <div className="space-y-2">
                {overdueEvents.slice(0, 3).map(event => (
                  <div key={event._id} className="bg-red-900/30 p-3 rounded-lg border border-red-700/30">
                    <p className="text-sm font-medium text-red-100">{event.contentData.title}</p>
                    <p className="text-xs text-red-400 mt-1">{formatEventDate(event.startDate)}</p>
                  </div>
                ))}
              </div>
            </Card>
          )}
        </div>
      </div>

      {/* Legend */}
      <Card className="bg-gradient-to-r from-[#0A1340]/80 to-[#1A237E]/80 border-blue-700/50 p-4 backdrop-blur-sm">
        <h4 className="text-lg font-semibold text-blue-100 mb-3">Legenda</h4>
        <div className="flex flex-wrap gap-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-purple-500/30 rounded"></div>
            <span className="text-purple-300">Evento Global</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500/30 rounded"></div>
            <span className="text-green-300">Evento de Grupo</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500/30 rounded"></div>
            <span className="text-blue-300">Evento Pessoal</span>
          </div>
        </div>
      </Card>

      {/* Day Action Modal */}
      <Dialog open={!!dayActionDate} onOpenChange={handleCloseDayActions}>
        <DialogContent className="bg-blue-950/90 backdrop-blur-md text-white border border-blue-500">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5 text-blue-400" />
              {dayActionDate?.toLocaleDateString('pt-BR', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-3">
            <Button 
              onClick={() => dayActionDate && handleViewDayEvents(dayActionDate)}
              variant="outline" 
              className="w-full justify-start text-blue-300 border-blue-400 hover:bg-blue-800/50"
            >
              <Eye className="w-4 h-4 mr-2" />
              Ver eventos do dia
            </Button>
            <Button 
              onClick={() => dayActionDate && handleAddEventForDay(dayActionDate)}
              variant="outline" 
              className="w-full justify-start text-green-300 border-green-400 hover:bg-green-800/50"
            >
              <Plus className="w-4 h-4 mr-2" />
              Adicionar novo evento
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Day Events Modal */}
      <Dialog open={showDayEvents} onOpenChange={() => setShowDayEvents(false)}>
        <DialogContent className="bg-blue-950/90 backdrop-blur-md text-white border border-blue-500 max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5 text-blue-400" />
              Eventos de {selectedDate?.toLocaleDateString('pt-BR', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {selectedDate && getEventsForDate(selectedDate.getDate()).length === 0 ? (
              <p className="text-blue-400 text-center py-8">Nenhum evento neste dia.</p>
            ) : (
              getEventsForDate(selectedDate?.getDate() || 0).map((event) => (
                <div key={event._id} className="bg-blue-900/30 p-4 rounded-lg border border-blue-700/50">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h4 className="font-semibold text-blue-100">{event.contentData.title}</h4>
                      <p className="text-blue-300 text-sm mt-1">{event.contentData.description}</p>
                      <div className="flex items-center gap-4 mt-2 text-xs text-blue-400">
                        <span>{getEventTypeDisplayName(event.type)}</span>
                        <span>{formatEventDate(event.startDate)}</span>
                        {event.isGlobal && <span className="text-purple-300">Global</span>}
                        {event.groupId && <span className="text-green-300">Grupo</span>}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setViewingEvent(event)}
                      className="text-blue-300 hover:text-blue-100"
                    >
                      Ver detalhes
                    </Button>
                  </div>
                </div>
              ))
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Event Details Modal */}
      <Dialog open={!!viewingEvent} onOpenChange={() => setViewingEvent(null)}>
        <DialogContent className="bg-blue-950/90 backdrop-blur-md text-white border border-blue-500 max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5 text-blue-400" />
              Detalhes do Evento
            </DialogTitle>
          </DialogHeader>
          {viewingEvent && (
            <div className="space-y-4">
              <div>
                <h3 className="text-xl font-bold text-blue-100">{viewingEvent.contentData.title}</h3>
                <p className="text-blue-300 mt-2">{viewingEvent.contentData.description}</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-blue-400">Tipo:</span>
                  <span className="ml-2 text-blue-100">{getEventTypeDisplayName(viewingEvent.type)}</span>
                </div>
                <div>
                  <span className="text-blue-400">Data:</span>
                  <span className="ml-2 text-blue-100">{formatEventDate(viewingEvent.startDate)}</span>
                </div>
                <div>
                  <span className="text-blue-400">Criador:</span>
                  <span className="ml-2 text-blue-100">{viewingEvent.ownerUsername}</span>
                </div>
                <div>
                  <span className="text-blue-400">Escopo:</span>
                  <span className="ml-2 text-blue-100">
                    {viewingEvent.isGlobal ? 'Global' : viewingEvent.groupId ? 'Grupo' : 'Pessoal'}
                  </span>
                </div>
              </div>
              
              <div className="flex justify-between pt-4">
                <div>
                  {viewingEvent.ownerUsername === authData?.username && (
                    <Button 
                      onClick={() => removeEvent(viewingEvent._id)}
                      variant="outline" 
                      className="text-red-300 border-red-400 hover:bg-red-800/50"
                    >
                      <X className="w-4 h-4 mr-2" />
                      Excluir
                    </Button>
                  )}
                </div>
                <Button
                  variant="outline"
                  onClick={() => setViewingEvent(null)}
                  className="text-blue-300 border-blue-400 hover:bg-blue-800/50"
                >
                  Fechar
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Add Event Modal */}
      <Dialog open={isAddingEvent} onOpenChange={() => {
        setIsAddingEvent(false)
        setSelectedDate(null)
        setNewEvent({
          type: 'TASK',
          contentData: { title: '', description: '', type: 'VIDEO' },
          startDate: '',
          endDate: '',
          isContinuous: false,
          groupId: ''
        })
      }}>
        <DialogContent className="bg-blue-950/90 backdrop-blur-md text-white border border-blue-500 max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Plus className="w-5 h-5 text-green-400" />
              Novo Evento de Professor
            </DialogTitle>
          </DialogHeader>
          
          {!selectedDate ? (
            <div className="space-y-4">
              <div className="text-center py-4">
                <Calendar className="w-12 h-12 text-blue-400 mx-auto mb-4" />
                <p className="text-gray-300 mb-2">Selecione a data para o novo evento</p>
              </div>

              <div>
                <Label htmlFor="eventDate" className="text-blue-300">Data do Evento*</Label>
                <Input
                  id="eventDate"
                  type="date"
                  value=""
                  onChange={(e) => {
                    if (e.target.value) {
                      const selectedEventDate = new Date(e.target.value + 'T09:00:00')
                      setSelectedDate(selectedEventDate)
                      setNewEvent(prev => ({
                        ...prev,
                        startDate: `${e.target.value}T09:00`
                      }))
                    }
                  }}
                  className="bg-blue-900/30 border-blue-600 text-white"
                  min={new Date().toISOString().split('T')[0]} // Cannot be before today
                />
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="bg-blue-900/30 p-3 rounded-lg">
                <p className="text-blue-200 text-sm">
                  Criando evento para: {selectedDate.toLocaleDateString('pt-BR', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title" className="text-blue-300">Título*</Label>
                  <Input
                    id="title"
                    value={newEvent.contentData?.title || ''}
                    onChange={(e) => setNewEvent(prev => ({
                      ...prev,
                      contentData: { ...prev.contentData!, title: e.target.value }
                    }))}
                    className="bg-blue-900/30 border-blue-600 text-white"
                    placeholder="Ex: Revisão de Matemática"
                  />
                </div>

                <div>
                  <Label htmlFor="eventType" className="text-blue-300">Tipo de Evento*</Label>
                  <Select
                    value={newEvent.type || 'TASK'}
                    onValueChange={(value: 'TASK' | 'ALERT' | 'INFO') => 
                      setNewEvent(prev => ({ ...prev, type: value }))
                    }
                  >
                    <SelectTrigger className="bg-blue-900/30 border-blue-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-blue-900 border-blue-600">
                      <SelectItem value="TASK">Tarefa</SelectItem>
                      <SelectItem value="ALERT">Alerta</SelectItem>
                      <SelectItem value="INFO">Informação</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="description" className="text-blue-300">Descrição*</Label>
                <Textarea
                  id="description"
                  value={newEvent.contentData?.description || ''}
                  onChange={(e) => setNewEvent(prev => ({
                    ...prev,
                    contentData: { ...prev.contentData!, description: e.target.value }
                  }))}
                  className="bg-blue-900/30 border-blue-600 text-white min-h-[80px]"
                  placeholder="Descrição detalhada do evento..."
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="startDate" className="text-blue-300">Data de Início*</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={newEvent.startDate ? newEvent.startDate.split('T')[0] : selectedDate?.toISOString().split('T')[0] || ''}
                    onChange={(e) => {
                      if (e.target.value) {
                        const timeString = newEvent.startDate ? newEvent.startDate.split('T')[1] || '09:00' : '09:00'
                        const newStartDate = new Date(e.target.value + 'T' + timeString)
                        setSelectedDate(newStartDate)
                        setNewEvent(prev => ({
                          ...prev,
                          startDate: `${e.target.value}T${timeString}`
                        }))
                      }
                    }}
                    className="bg-blue-900/30 border-blue-600 text-white"
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>

                <div>
                  <Label htmlFor="time" className="text-blue-300">Horário*</Label>
                  <Input
                    id="time"
                    type="time"
                    value={newEvent.startDate ? newEvent.startDate.split('T')[1]?.slice(0, 5) || '09:00' : '09:00'}
                    onChange={(e) => {
                      const dateStr = newEvent.startDate ? newEvent.startDate.split('T')[0] : selectedDate?.toISOString().split('T')[0] || ''
                      setNewEvent(prev => ({
                        ...prev,
                        startDate: `${dateStr}T${e.target.value}`
                      }))
                    }}
                    className="bg-blue-900/30 border-blue-600 text-white"
                  />
                </div>
              </div>

              {/* End Date - only show if continuous */}
              {newEvent.isContinuous && (
                <div>
                  <Label htmlFor="endDate" className="text-blue-300">Data de Fim*</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={newEvent.endDate ? newEvent.endDate.split('T')[0] : ''}
                    onChange={(e) => {
                      if (e.target.value) {
                        // Set end date to same time as start date
                        const timeString = newEvent.startDate ? newEvent.startDate.split('T')[1] || '09:00' : '09:00'
                        setNewEvent(prev => ({
                          ...prev,
                          endDate: `${e.target.value}T${timeString}`
                        }))
                      } else {
                        setNewEvent(prev => ({
                          ...prev,
                          endDate: ''
                        }))
                      }
                    }}
                    min={newEvent.startDate ? newEvent.startDate.split('T')[0] : selectedDate?.toISOString().split('T')[0]} // Cannot be before start date
                    className="bg-blue-900/30 border-blue-600 text-white"
                  />
                  <p className="text-xs text-blue-400 mt-1">
                    O evento aparecerá em todos os dias entre a data de início e fim
                  </p>
                </div>
              )}

              <div>
                <Label htmlFor="group" className="text-blue-300">Grupo (opcional)</Label>
                {isGroupsLoading ? (
                  <div className="bg-blue-900/30 border-blue-600 p-3 rounded-md flex items-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin text-blue-400" />
                    <span className="text-blue-300">Carregando grupos...</span>
                  </div>
                ) : (
                  <Select
                    value={newEvent.groupId || 'personal'}
                    onValueChange={(value) => setNewEvent(prev => ({ 
                      ...prev, 
                      groupId: value === 'personal' ? '' : value 
                    }))}
                  >
                    <SelectTrigger className="bg-blue-900/30 border-blue-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-blue-900 border-blue-600">
                      <SelectItem value="personal">Evento Pessoal</SelectItem>
                      {groups.map(group => (
                        <SelectItem key={group.id} value={group.id}>
                          {group.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isContinuous"
                    checked={newEvent.isContinuous || false}
                    onCheckedChange={(checked) => {
                      setNewEvent(prev => ({ 
                        ...prev, 
                        isContinuous: !!checked,
                        // Clear endDate if not continuous
                        endDate: checked ? prev.endDate : ''
                      }))
                    }}
                  />
                  <Label htmlFor="isContinuous" className="text-blue-300 text-sm">
                    Evento Contínuo (se estende por vários dias)
                  </Label>
                </div>
              </div>

              <div className="flex justify-end gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsAddingEvent(false)
                    setSelectedDate(null)
                    setNewEvent({
                      type: 'TASK',
                      contentData: { title: '', description: '', type: 'VIDEO' },
                      startDate: '',
                      endDate: '',
                      isContinuous: false,
                      groupId: ''
                    })
                  }}
                  className="text-gray-300 border-gray-600 hover:bg-gray-800/50"
                >
                  Cancelar
                </Button>
                <Button
                  onClick={addEvent}
                  disabled={
                    isUpdatingEvent || 
                    !newEvent.contentData?.title?.trim() || 
                    !newEvent.contentData?.description?.trim() ||
                    !newEvent.startDate ||
                    (newEvent.isContinuous && !newEvent.endDate)
                  }
                  className="bg-green-600 hover:bg-green-700 text-white disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isUpdatingEvent ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Criando...
                    </>
                  ) : (
                    <>
                      <Plus className="w-4 h-4 mr-2" />
                      Criar Evento
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default TeacherCalendar
