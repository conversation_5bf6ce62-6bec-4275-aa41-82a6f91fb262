"use client"

import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  X,
  Users, 
  School, 
  BookOpen, 
  BarChart3, 
  Star,
  UserCheck,
  Crown,
  Target,
  TrendingUp,
  Calendar,
  Award,
  Globe,
  ArrowLeft,
  Eye,
  Activity,
  Clock,
  CheckCircle2,
  Zap
} from 'lucide-react'
import { ITeacherGroup, IGroupProgress } from '@/services/CDS/interfaces/teacherManager'
import TeacherManagerController from '@/services/CDS/api/TeacherManagerController'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts'

// Interfaces for the old-style group performance chart
interface TimeRange {
  start: string;
  end: string;
}

interface QuestionMetrics {
  totalQuestions: number;
  correctAnswers: number;
  averageScore: number;
  completionRate: number;
}

interface LessonMetrics {
  totalLessons: number;
  completedLessons: number;
  averageProgress: number;
}

interface Engagement {
  activityTime: number;
}

interface PeriodMetrics {
  questionMetrics: QuestionMetrics;
  lessonMetrics: LessonMetrics;
  engagement: Engagement;
}

interface Period {
  period: number;
  timeRange: TimeRange;
  metrics: PeriodMetrics;
}

interface AggregatedPeriod {
  period: number;
  timeRange: TimeRange;
  students: number;
  totalPerformance: number;
  avgPerformance: number;
  totalLessons: number;
  totalQuestions: number;
  totalTime: number;
}

interface StudentProgressData {
  userId: string;
  name: string;
  email: string;
  timeAggregation: string;
  range: number;
  periods: Period[];
  metrics?: {
    questionMetrics?: any[];
    lessonMetrics?: any[];
    tracking?: {
      totalTimeSpent: number;
    };
  };
}

interface ProgressResponse {
  message: string;
  data: StudentProgressData[];
}

interface GroupDetailsProps {
  groupId: string;
  onGroupUpdate: () => void;
  onClose: () => void;
}

export const GroupDetails: React.FC<GroupDetailsProps> = ({
  groupId,
  onGroupUpdate,
  onClose
}) => {
  const [group, setGroup] = useState<ITeacherGroup | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'members' | 'performance'>('overview')
  const [groupProgress, setGroupProgress] = useState<IGroupProgress[]>([])
  const [isProgressLoading, setIsProgressLoading] = useState(false)
  const [groupPerformanceData, setGroupPerformanceData] = useState<any | null>(null)
  const [showGroupPerformance, setShowGroupPerformance] = useState(false)
  const [groupPerformanceLoading, setGroupPerformanceLoading] = useState(false)
  const [hoveredPeriod, setHoveredPeriod] = useState<Period | AggregatedPeriod | null>(null)

  // Function to calculate performance score
  const calculatePerformanceScore = useCallback((period: Period): number => {
    const lessonProgress = (period.metrics.lessonMetrics.totalLessons || 0) * 25;
    const questionCorrectRate = (period.metrics.questionMetrics.totalQuestions || 0) * 35;
    const totalEngagementNumber = period.metrics.questionMetrics.totalQuestions + period.metrics.lessonMetrics.totalLessons;
    let totalEngagementRate = 50*totalEngagementNumber%10;
    let engagementActivityScore = period.metrics.engagement.activityTime;
    if(engagementActivityScore > 0){
      engagementActivityScore = Math.ceil(30 + engagementActivityScore)
    }
    if(engagementActivityScore > 300){
      engagementActivityScore = 300;
    }
    if(engagementActivityScore < 0){
      engagementActivityScore = 0;
    }
    
    const performanceScore = (lessonProgress) + (questionCorrectRate) + (totalEngagementRate) + (engagementActivityScore);
    return performanceScore;
  }, []);

  const handleShowGroupPerformance = useCallback(async () => {
    if (!groupId) return;
    
    console.log('🚀 Starting handleShowGroupPerformance with groupId:', groupId);
    setGroupPerformanceLoading(true);
    try {
      const rawResponse = await TeacherManagerController.getStudentProgress(groupId) as unknown;
      const response = rawResponse as ProgressResponse | StudentProgressData[];
      
      let studentsData: StudentProgressData[] = [];
      
      if (response && typeof response === 'object' && 'data' in response && Array.isArray((response as ProgressResponse).data)) {
        studentsData = (response as ProgressResponse).data;
      } else if (Array.isArray(response)) {
        studentsData = response as StudentProgressData[];
      }
      
      const periodAggregation: { [periodKey: string]: AggregatedPeriod } = {};
      
      studentsData.forEach(studentData => {
        if (studentData.periods && studentData.periods.length > 0) {
          studentData.periods.forEach(period => {
            const periodKey = `${period.period}-${period.timeRange.start}`;
            
            if (!periodAggregation[periodKey]) {
              periodAggregation[periodKey] = {
                period: period.period,
                timeRange: period.timeRange,
                students: 0,
                totalPerformance: 0,
                avgPerformance: 0,
                totalLessons: 0,
                totalQuestions: 0,
                totalTime: 0
              };
            }
            
            const performanceScore = calculatePerformanceScore(period);
            periodAggregation[periodKey].students += 1;
            periodAggregation[periodKey].totalPerformance += performanceScore;
            periodAggregation[periodKey].totalLessons += period.metrics.lessonMetrics.totalLessons || 0;
            periodAggregation[periodKey].totalQuestions += period.metrics.questionMetrics.totalQuestions || 0;
            periodAggregation[periodKey].totalTime += period.metrics.engagement.activityTime || 0;
          });
        }
      });
      
      // Calculate averages and prepare final data
      const aggregatedPeriods = Object.values(periodAggregation).map(period => ({
        ...period,
        avgPerformance: period.students > 0 ? period.totalPerformance / period.students : 0
      }));
      
      // Sort by period number
      aggregatedPeriods.sort((a, b) => a.period - b.period);
      
      setGroupPerformanceData({
        totalStudents: studentsData.length,
        periods: aggregatedPeriods
      });
      setShowGroupPerformance(true);
      console.log('✅ Group performance data set successfully:', {
        totalStudents: studentsData.length,
        periodsCount: aggregatedPeriods.length,
        showGroupPerformance: true
      });
    } catch (error) {
      console.error('Error fetching group performance:', error instanceof Error ? error.message : String(error));
    } finally {
      setGroupPerformanceLoading(false);
    }
  }, [groupId, calculatePerformanceScore]);

  useEffect(() => {
    const fetchGroupDetails = async () => {
      try {
        setIsLoading(true)
        // Fetch detailed group information
        const response = await TeacherManagerController.getTeacherGroups('current')
        const foundGroup = response.groups.find(g => g.id === groupId)
        setGroup(foundGroup || null)
        
        // Automatically load group performance when group is loaded
        if (foundGroup) {
          console.log('🎯 About to call handleShowGroupPerformance for group:', foundGroup.id);
          await handleShowGroupPerformance()
        }
      } catch (error) {
        console.error('Error fetching group details:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchGroupDetails()
  }, [groupId, handleShowGroupPerformance])

  const fetchGroupProgress = async () => {
    if (!group) return
    
    try {
      setIsProgressLoading(true)
      const progressData = await TeacherManagerController.getGroupProgress([group.id], 'MONTH', 6)
      setGroupProgress(progressData)
    } catch (error) {
      console.error('Error fetching group progress:', error)
      setGroupProgress([])
    } finally {
      setIsProgressLoading(false)
    }
  }

  const getGroupIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'school':
        return <School className="w-8 h-8 text-blue-400" />;
      case 'class':
        return <BookOpen className="w-8 h-8 text-purple-400" />;
      case 'school_year':
        return <Crown className="w-8 h-8 text-green-400" />;
      default:
        return <Users className="w-8 h-8 text-cyan-400" />;
    }
  }

  const formatCategory = (category: string): string => {
    switch (category.toLowerCase()) {
      case 'school':
        return 'Escola';
      case 'class':
        return 'Turma';
      case 'school_year':
        return 'Ano Letivo';
      case 'generic':
        return 'Genérico';
      default:
        return category;
    }
  }

  const getCategoryGradient = (category: string): string => {
    switch (category.toLowerCase()) {
      case 'school':
        return 'from-blue-600 to-blue-700';
      case 'class':
        return 'from-purple-600 to-purple-700';
      case 'school_year':
        return 'from-green-600 to-green-700';
      default:
        return 'from-cyan-600 to-cyan-700';
    }
  }

  if (isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      >
        <Card className="bg-gradient-to-b from-blue-900/90 to-purple-900/90 border-blue-500/30 p-8 max-w-md w-full text-center">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="w-16 h-16 mx-auto mb-4 border-4 border-blue-500 border-t-transparent rounded-full"
          />
          <h3 className="text-xl font-bold text-blue-100 mb-2">Carregando Detalhes</h3>
          <p className="text-blue-300">Buscando informações do grupo...</p>
        </Card>
      </motion.div>
    )
  }

  if (!group) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      >
        <Card className="bg-gradient-to-b from-red-900/90 to-red-800/90 border-red-500/30 p-8 max-w-md w-full text-center">
          <X className="w-16 h-16 mx-auto mb-4 text-red-400" />
          <h3 className="text-xl font-bold text-red-100 mb-2">Grupo Não Encontrado</h3>
          <p className="text-red-300 mb-4">Não foi possível carregar os detalhes do grupo.</p>
          <Button onClick={onClose} variant="outline" className="border-red-500 text-red-300 hover:bg-red-500/20">
            Fechar
          </Button>
        </Card>
      </motion.div>
    )
  }

  const totalStudents = group.memberClassification.students.length
  const totalTeachers = group.memberClassification.teachers.length
  const totalCoordinators = group.memberClassification.coordinators.length
  const totalMembers = totalStudents + totalTeachers + totalCoordinators

  const getChartData = (metric: 'questionsCompleted' | 'lessonsCompleted') => {
    if (!groupProgress.length || !groupProgress[0].periods.length) return []

    return groupProgress[0].periods.map((period, index) => ({
      period: `Período ${period.period}`,
      value: metric === 'questionsCompleted' 
        ? period.metrics.questionMetrics.totalQuestions
        : period.metrics.lessonMetrics.completedLessons
    }))
  }

  // Helper function to format date range
  const formatDateRange = (start: string, end: string) => {
    const startDate = new Date(start);
    const endDate = new Date(end);
    return `${startDate.toLocaleDateString('pt-BR', {day: 'numeric', month: 'short'})} - ${endDate.toLocaleDateString('pt-BR', {day: 'numeric', month: 'short'})}`;
  };

  // Skeleton loading component for performance chart
  const PerformanceChartSkeleton = () => (
    <div className="mb-6 p-4 bg-blue-900/20 rounded-lg border border-blue-500/30">
      <div className="flex justify-between items-center mb-4">
        <div className="h-6 bg-blue-800/30 rounded w-64 animate-pulse"></div>
      </div>
      
      <div className="h-44 bg-blue-950/30 rounded p-3 flex items-end justify-between">
        {[...Array(6)].map((_, index) => (
          <div key={index} className="flex flex-col items-center w-1/6">
            <div className="w-5/6">
              <div 
                style={{ height: `${Math.random() * 80 + 20}px` }}
                className="bg-blue-700/30 rounded-t w-full animate-pulse"
              />
            </div>
            <div className="text-xs mt-1 text-center">
              <div className="h-3 bg-blue-800/30 rounded w-8 animate-pulse"></div>
              <div className="h-2 bg-blue-800/20 rounded w-6 mt-1 animate-pulse"></div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="flex justify-between text-xs text-blue-400 mt-2">
        <div className="h-3 bg-blue-800/30 rounded w-48 animate-pulse"></div>
        <div className="h-3 bg-blue-800/30 rounded w-64 animate-pulse"></div>
      </div>
    </div>
  );

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.9, y: 20 }}
        onClick={(e) => e.stopPropagation()}
        className="bg-gradient-to-b from-[#040A2F] to-[#0F2057] rounded-2xl border border-blue-500/30 shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="relative overflow-hidden">
          <div className="pointer-events-none absolute -top-24 -right-40 h-[300px] w-[300px] rounded-full bg-blue-600/20 blur-3xl" />
          <div className="pointer-events-none absolute -top-24 -left-40 h-[300px] w-[300px] rounded-full bg-purple-600/10 blur-3xl" />
          
          <div className="relative p-6 border-b border-blue-500/20">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-4">
                <div className={`p-4 rounded-2xl bg-gradient-to-r ${getCategoryGradient(group.category)} shadow-lg`}>
                  {getGroupIcon(group.category)}
                </div>
                <div>
                  <Badge variant="secondary" className="bg-blue-600/20 text-blue-300 border-blue-500/30 mb-2">
                    {formatCategory(group.category)}
                  </Badge>
                  <h2 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-300">
                    {group.name}
                  </h2>
                  <p className="text-blue-400 mt-1 max-w-md">
                    {group.description || 'Grupo de estudantes para acompanhamento de progresso e atividades.'}
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="text-blue-300 hover:text-blue-100 hover:bg-blue-600/20"
              >
                <X className="w-6 h-6" />
              </Button>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="px-6 border-b border-blue-500/20">
          <div className="flex space-x-8">
            <button
              onClick={() => setActiveTab('overview')}
              className={`py-4 px-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'overview'
                  ? 'border-blue-400 text-blue-100'
                  : 'border-transparent text-blue-300 hover:text-blue-100 hover:border-blue-500/50'
              }`}
            >
              <div className="flex items-center gap-2">
                <Eye className="w-4 h-4" />
                Visão Geral
              </div>
            </button>
            <button
              onClick={() => setActiveTab('members')}
              className={`py-4 px-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'members'
                  ? 'border-blue-400 text-blue-100'
                  : 'border-transparent text-blue-300 hover:text-blue-100 hover:border-blue-500/50'
              }`}
            >
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Membros
              </div>
            </button>
            <button
              onClick={() => {
                setActiveTab('performance')
                if (groupProgress.length === 0) {
                  fetchGroupProgress()
                }
              }}
              className={`py-4 px-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'performance'
                  ? 'border-blue-400 text-blue-100'
                  : 'border-transparent text-blue-300 hover:text-blue-100 hover:border-blue-500/50'
              }`}
            >
              <div className="flex items-center gap-2">
                <BarChart3 className="w-4 h-4" />
                Desempenho
              </div>
            </button>
          </div>
        </div>

        {/* Content Area */}
        <div className="p-6 overflow-y-auto max-h-[50vh]">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="bg-gradient-to-r from-blue-900/20 to-blue-800/10 border-blue-500/30 p-4">
                  <h4 className="text-lg font-bold text-blue-100 mb-3 flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    Composição do Grupo
                  </h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-2 bg-blue-900/20 rounded">
                      <span className="text-blue-200 text-sm">Total de Estudantes</span>
                      <span className="text-blue-100 font-bold">{totalStudents}</span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-purple-900/20 rounded">
                      <span className="text-purple-200 text-sm">Total de Professores</span>
                      <span className="text-purple-100 font-bold">{totalTeachers}</span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-green-900/20 rounded">
                      <span className="text-green-200 text-sm">Total de Coordenadores</span>
                      <span className="text-green-100 font-bold">{totalCoordinators}</span>
                    </div>
                  </div>
                </Card>

                <Card className="bg-gradient-to-r from-green-900/20 to-green-800/10 border-green-500/30 p-4">
                  <h4 className="text-lg font-bold text-green-100 mb-3 flex items-center gap-2">
                    <Activity className="w-5 h-5" />
                    Informações do Grupo
                  </h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-2 bg-green-900/20 rounded">
                      <span className="text-green-200 text-sm">Categoria</span>
                      <span className="text-green-100 font-bold">{formatCategory(group.category)}</span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-green-900/20 rounded">
                      <span className="text-green-200 text-sm">Criado em</span>
                      <span className="text-green-100 font-bold">
                        {new Date(group.createdAt).toLocaleDateString('pt-BR')}
                      </span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-green-900/20 rounded">
                      <span className="text-green-200 text-sm">Status</span>
                      <span className="text-green-100 font-bold">
                        {group.inactive ? 'Inativo' : 'Ativo'}
                      </span>
                    </div>
                  </div>
                </Card>
              </div>

              {/* Performance Chart in Overview */}
              {(() => {
                console.log('🎯 Render state check:', {
                  groupPerformanceLoading,
                  showGroupPerformance,
                  hasGroupPerformanceData: !!groupPerformanceData,
                  periodsCount: groupPerformanceData?.periods?.length || 0
                });
                return null;
              })()}
              {groupPerformanceLoading ? (
                <PerformanceChartSkeleton />
              ) : showGroupPerformance && groupPerformanceData ? (
                <div className="mb-6 p-4 bg-blue-900/20 rounded-lg border border-blue-500/30">
                  <div className="flex justify-between items-center mb-4">
                    <h4 className="text-lg font-bold text-blue-100 flex items-center gap-2">
                      <BarChart3 className="w-5 h-5" />
                      Performance Acumulada do Grupo por Período
                    </h4>
                  </div>
                  
                  <div className="h-44 bg-blue-950/30 rounded p-3 flex items-end justify-between">
                    {groupPerformanceData.periods.map((period: AggregatedPeriod) => {
                      const barHeight = Math.min(140, Math.max(
                        (period.avgPerformance / 100) * 140,
                        8
                      ));
                      
                      const getBarColor = (score: number) => {
                        if (score >= 80) return 'bg-green-500 hover:bg-green-400';
                        if (score >= 60) return 'bg-yellow-500 hover:bg-yellow-400';
                        if (score >= 40) return 'bg-orange-500 hover:bg-orange-400';
                        return 'bg-red-500 hover:bg-red-400';
                      };
                      
                      return (
                        <div
                          key={`${period.period}-${period.timeRange.start}`}
                          className="flex flex-col items-center relative group cursor-pointer w-1/6"
                          onMouseEnter={() => setHoveredPeriod(period)}
                          onMouseLeave={() => setHoveredPeriod(null)}
                        >
                          <div className="w-5/6 flex justify-center">
                            <div
                              className={`${getBarColor(period.avgPerformance)} rounded-t transition-all duration-300 shadow-lg w-full`}
                              style={{ height: `${barHeight}px` }}
                            />
                          </div>
                          <div className="text-xs mt-1 text-center">
                            <p className="text-blue-200 font-semibold">P{period.period}</p>
                            <p className="text-blue-400 text-[10px]">
                              {period.avgPerformance.toFixed(0)}%
                            </p>
                          </div>
                          
                          {hoveredPeriod && hoveredPeriod.period === period.period && (
                            <div className="absolute bottom-full mb-2 bg-gray-900 text-white p-2 rounded shadow-lg z-20 text-xs w-48 left-1/2 transform -translate-x-1/2">
                              <div className="font-bold text-center mb-1">Período {period.period}</div>
                              <div className="text-center text-gray-300 mb-2">
                                {period.timeRange.start} - {period.timeRange.end}
                              </div>
                              <div className="space-y-1">
                                <div>Performance Média: <span className="font-bold text-blue-300">{period.avgPerformance.toFixed(1)}%</span></div>
                                <div>Estudantes: <span className="font-bold text-green-300">{period.students}</span></div>
                                <div>Total de Aulas: <span className="font-bold text-purple-300">{period.totalLessons}</span></div>
                                <div>Total de Questões: <span className="font-bold text-yellow-300">{period.totalQuestions}</span></div>
                                <div>Tempo Total: <span className="font-bold text-orange-300">{Math.round(period.totalTime / 60)}min</span></div>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                  
                  <div className="flex justify-between text-xs text-blue-400 mt-2">
                    <span>Período com menor performance: {
                      groupPerformanceData.periods.length > 0 
                        ? groupPerformanceData.periods.reduce((min: AggregatedPeriod, p: AggregatedPeriod) => p.avgPerformance < min.avgPerformance ? p : min).avgPerformance.toFixed(1)
                        : 'N/A'
                    }%</span>
                    <span>Período com maior performance: {
                      groupPerformanceData.periods.length > 0 
                        ? groupPerformanceData.periods.reduce((max: AggregatedPeriod, p: AggregatedPeriod) => p.avgPerformance > max.avgPerformance ? p : max).avgPerformance.toFixed(1)
                        : 'N/A'
                    }%</span>
                  </div>
                </div>
              ) : null}

              
            </div>
          )}

          {activeTab === 'members' && (
            <div className="space-y-6">
              {/* Students */}
              {totalStudents > 0 && (
                <div>
                  <h3 className="text-xl font-bold text-blue-100 mb-4 flex items-center gap-2">
                    <Users className="w-5 h-5 text-blue-400" />
                    Estudantes ({totalStudents})
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {group.memberClassification.students.map((student, index) => (
                      <motion.div
                        key={student._id || index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.05 }}
                        className="bg-gradient-to-r from-blue-900/30 to-blue-800/20 rounded-lg p-3 border border-blue-500/20 hover:border-blue-400/40 transition-colors"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
                            <span className="text-white font-bold text-sm">
                              {student.name?.charAt(0).toUpperCase() || 'A'}
                            </span>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-blue-100 font-medium truncate">{student.name || 'Nome não disponível'}</p>
                            <p className="text-blue-400 text-xs truncate">{student.email || 'Email não disponível'}</p>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}

              {/* Teachers */}
              {totalTeachers > 0 && (
                <div>
                  <h3 className="text-xl font-bold text-purple-100 mb-4 flex items-center gap-2">
                    <Crown className="w-5 h-5 text-purple-400" />
                    Professores ({totalTeachers})
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {group.memberClassification.teachers.map((teacher, index) => (
                      <motion.div
                        key={teacher._id || index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.05 }}
                        className="bg-gradient-to-r from-purple-900/30 to-purple-800/20 rounded-lg p-3 border border-purple-500/20 hover:border-purple-400/40 transition-colors"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center">
                            <Crown className="w-5 h-5 text-white" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-purple-100 font-medium truncate">{teacher.name || 'Nome não disponível'}</p>
                            <p className="text-purple-400 text-xs truncate">{teacher.email || 'Email não disponível'}</p>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}

              {/* Coordinators */}
              {totalCoordinators > 0 && (
                <div>
                  <h3 className="text-xl font-bold text-green-100 mb-4 flex items-center gap-2">
                    <Target className="w-5 h-5 text-green-400" />
                    Coordenadores ({totalCoordinators})
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {group.memberClassification.coordinators.map((coordinator, index) => (
                      <motion.div
                        key={coordinator._id || index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.05 }}
                        className="bg-gradient-to-r from-green-900/30 to-green-800/20 rounded-lg p-3 border border-green-500/20 hover:border-green-400/40 transition-colors"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-gradient-to-r from-green-600 to-emerald-600 rounded-full flex items-center justify-center">
                            <Target className="w-5 h-5 text-white" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-green-100 font-medium truncate">{coordinator.name || 'Nome não disponível'}</p>
                            <p className="text-green-400 text-xs truncate">{coordinator.email || 'Email não disponível'}</p>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'performance' && (
            <div className="space-y-6">
              {isProgressLoading ? (
                <Card className="p-8 flex justify-center items-center">
                  <div className="flex flex-col items-center">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mb-4"
                    />
                    <p className="text-blue-300">Carregando dados de desempenho...</p>
                  </div>
                </Card>
              ) : groupProgress.length > 0 ? (
                <>
                  {/* Performance Charts */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card className="bg-gradient-to-r from-blue-900/20 to-blue-800/10 border-blue-500/30 p-6">
                      <h4 className="text-lg font-bold text-blue-100 mb-4 flex items-center gap-2">
                        <BarChart3 className="w-5 h-5" />
                        Questões Completadas
                      </h4>
                      <div className="h-[300px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart data={getChartData('questionsCompleted')}>
                            <CartesianGrid strokeDasharray="3 3" stroke="#1e3a8a" opacity={0.3} />
                            <XAxis 
                              dataKey="period" 
                              stroke="#93c5fd"
                              tickLine={false}
                              axisLine={false}
                              tick={{ fontSize: 12 }}
                            />
                            <YAxis 
                              stroke="#93c5fd"
                              tickLine={false}
                              axisLine={false}
                              tick={{ fontSize: 12 }}
                            />
                            <Tooltip 
                              contentStyle={{
                                backgroundColor: '#0F2057',
                                border: '1px solid #3B82F6',
                                borderRadius: '8px',
                                color: '#DBEAFE'
                              }}
                            />
                            <Line
                              type="monotone"
                              dataKey="value"
                              stroke="#3B82F6"
                              strokeWidth={3}
                              dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                              activeDot={{ r: 6, fill: '#60A5FA' }}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    </Card>

                    <Card className="bg-gradient-to-r from-purple-900/20 to-purple-800/10 border-purple-500/30 p-6">
                      <h4 className="text-lg font-bold text-purple-100 mb-4 flex items-center gap-2">
                        <TrendingUp className="w-5 h-5" />
                        Aulas Assistidas
                      </h4>
                      <div className="h-[300px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart data={getChartData('lessonsCompleted')}>
                            <CartesianGrid strokeDasharray="3 3" stroke="#581c87" opacity={0.3} />
                            <XAxis 
                              dataKey="period" 
                              stroke="#c4b5fd"
                              tickLine={false}
                              axisLine={false}
                              tick={{ fontSize: 12 }}
                            />
                            <YAxis 
                              stroke="#c4b5fd"
                              tickLine={false}
                              axisLine={false}
                              tick={{ fontSize: 12 }}
                            />
                            <Tooltip 
                              contentStyle={{
                                backgroundColor: '#581C87',
                                border: '1px solid #A855F7',
                                borderRadius: '8px',
                                color: '#E9D5FF'
                              }}
                            />
                            <Line
                              type="monotone"
                              dataKey="value"
                              stroke="#A855F7"
                              strokeWidth={3}
                              dot={{ fill: '#A855F7', strokeWidth: 2, r: 4 }}
                              activeDot={{ r: 6, fill: '#C084FC' }}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    </Card>
                  </div>

                  {/* Performance Metrics */}
                  <Card className="bg-gradient-to-r from-green-900/20 to-green-800/10 border-green-500/30 p-6">
                    <h4 className="text-lg font-bold text-green-100 mb-4 flex items-center gap-2">
                      <Star className="w-5 h-5" />
                      Métricas de Desempenho
                    </h4>
                    {groupProgress[0] && groupProgress[0].periods.length > 0 && (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-green-900/20 rounded-lg p-4">
                          <div className="text-sm text-green-300 mb-1">Taxa de Conclusão</div>
                          <div className="text-2xl font-bold text-green-100">
                            {Math.round(groupProgress[0].periods[groupProgress[0].periods.length - 1]?.metrics.questionMetrics.completionRate || 0)}%
                          </div>
                        </div>
                        <div className="bg-blue-900/20 rounded-lg p-4">
                          <div className="text-sm text-blue-300 mb-1">Pontuação Média</div>
                          <div className="text-2xl font-bold text-blue-100">
                            {Math.round(groupProgress[0].periods[groupProgress[0].periods.length - 1]?.metrics.questionMetrics.averageScore || 0)}%
                          </div>
                        </div>
                        <div className="bg-purple-900/20 rounded-lg p-4">
                          <div className="text-sm text-purple-300 mb-1">Estudantes Ativos</div>
                          <div className="text-2xl font-bold text-purple-100">
                            {groupProgress[0].periods[groupProgress[0].periods.length - 1]?.metrics.engagement.activeStudents || 0}
                          </div>
                        </div>
                      </div>
                    )}
                  </Card>
                </>
              ) : (
                <Card className="p-8 flex justify-center items-center">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 text-blue-500/50 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-blue-300 mb-2">Dados não disponíveis</h3>
                    <p className="text-blue-400">Não foi possível carregar os dados de desempenho para este grupo.</p>
                  </div>
                </Card>
              )}
            </div>
          )}
        </div>        
      </motion.div>
    </motion.div>
  )
}
