"use client"

import { motion } from 'framer-motion'
import { Lock, Eye, EyeOff, AlertCircle, Loader2 } from 'lucide-react'
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/context/AuthContext'
import Image from 'next/image'
import logoazul from "@/assets/images/logoazul.png"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { useState } from 'react'

const loginSchema = z.object({
  email: z.string().email("Endereço de email inválido"),
  password: z.string().min(6, "A senha deve ter pelo menos 6 caracteres"),
  remember: z.boolean().optional()
})

type LoginFormValues = z.infer<typeof loginSchema>

export default function Login() {
  const { login, authData } = useAuth()
  const router = useRouter()
  const [showPassword, setShowPassword] = useState(false)
  const [forgotPasswordModalOpen, setForgotPasswordModalOpen] = useState(false)
  const [errorModalOpen, setErrorModalOpen] = useState(false)
  const [errorMessage, setErrorMessage] = useState("Ocorreu um erro durante o login.")  
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
      remember: false
    }
  })

  const onSubmit = async (data: LoginFormValues) => {
    setIsLoading(true)
    try {
      const { success, user } = await login({
        email: data.email,
        password: data.password
      })
      console.log("user[debug]",user)
      if (success && user) {
        switch (user.role) {
          case 'student':
            router.push('/inicio')
            break
          case 'teacher':
            router.push('/professor')
            break
          case 'coordinator':
            router.push('/coordinator')
            break
          case 'admin':
            router.push('/gestor')
            break
          case 'admin':
            router.push('/acoes')
            break
          default:
            console.error('Role não reconhecida:', user.role)
            router.push('/inicio') // Fallback para rota padrão
            break
        }
      } else {
        setErrorMessage("Credenciais inválidas. Verifique seu email e senha e tente novamente.")
        setErrorModalOpen(true)
      }
    } catch (error) {
      console.error('Erro durante o login:', error)
      setErrorMessage("Ocorreu um erro ao tentar fazer login. Por favor, tente novamente mais tarde.")
      setErrorModalOpen(true)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (event: React.KeyboardEvent<HTMLFormElement>) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      form.handleSubmit(onSubmit)();
    }
  }

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  const handleForgotPassword = (e: React.MouseEvent) => {
    e.preventDefault()
    setForgotPasswordModalOpen(true)
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#040A2F] to-[#0F2057] text-blue-100 py-8 px-4 sm:px-6 lg:px-8 overflow-hidden relative flex items-center justify-center">
      {/* Estrelas de fundo */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(100)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute h-1 w-1 bg-white rounded-full"
            initial={{ opacity: 0.1, scale: 0.5 }}
            animate={{ 
              opacity: [0.1, 1, 0.1], 
              scale: [0.5, 1, 0.5],
            }}
            transition={{ 
              duration: Math.random() * 3 + 2, 
              repeat: Infinity,
              ease: "easeInOut"
            }}
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
            }}
          />
        ))}
      </div>

      <Card className="w-full max-w-md bg-blue-900/30 backdrop-blur-md border-blue-500/30">
        <CardHeader className="space-y-1">
          <div className="flex justify-center items-center mb-4">
            <Image
              src={logoazul}
              alt="Atomize Logo"
              width={50}
              height={50}
              className="brightness-200 mr-2"
            />
            <span className="text-2xl font-semibold text-white">Atomize</span>
          </div>
          <CardTitle className="text-2xl font-bold text-center text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">
            Entre na sua conta cósmica
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} onKeyDown={handleKeyDown} className="space-y-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="email"
                        placeholder="<EMAIL>"
                        className="bg-blue-800/50 border-blue-500/50 placeholder-blue-300 text-blue-100"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Senha</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          {...field}
                          type={showPassword ? "text" : "password"}
                          placeholder="••••••••"
                          className="bg-blue-800/50 border-blue-500/50 placeholder-blue-300 text-blue-100 pr-10"
                          disabled={isLoading}
                        />
                        <div
                          onClick={togglePasswordVisibility}
                          className="absolute inset-y-0 right-0 pr-3 flex items-center text-blue-300 hover:text-blue-100 cursor-pointer"
                        >
                          {showPassword ? (
                            <EyeOff className="h-5 w-5" />
                          ) : (
                            <Eye className="h-5 w-5" />
                          )}
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex items-center justify-between">
                <FormField
                  control={form.control}
                  name="remember"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-2">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="border-blue-500"
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormLabel className="text-sm text-blue-200">Lembrar-me</FormLabel>
                    </FormItem>
                  )}
                />
                <button
                  onClick={handleForgotPassword}
                  className="text-sm text-blue-400 hover:text-blue-300"
                >
                  Esqueceu sua senha?
                </button>
              </div>
              <Button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Entrando...
                  </>
                ) : (
                  <>
                    <Lock className="mr-2 h-4 w-4" /> 
                    Entrar
                  </>
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Modal de Esqueceu Senha */}
      <Dialog open={forgotPasswordModalOpen} onOpenChange={setForgotPasswordModalOpen}>
        <DialogContent className="bg-blue-900/80 backdrop-blur-md border-blue-500/30 text-blue-100">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">
              Esqueceu sua senha?
            </DialogTitle>
            <DialogDescription className="text-blue-200 mt-2">
              Por favor, entre em contato com seu professor para ver novamente sua senha.
            </DialogDescription>
          </DialogHeader>
        
          
          <DialogFooter>
            <Button 
              onClick={() => setForgotPasswordModalOpen(false)}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Entendi
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Erro de Login */}
      <Dialog open={errorModalOpen} onOpenChange={setErrorModalOpen}>
        <DialogContent className="bg-blue-900/80 backdrop-blur-md border-red-500/30 text-blue-100">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-red-400 to-orange-400 flex items-center gap-2">
              <AlertCircle className="h-6 w-6 text-red-400" />
              Falha no Login
            </DialogTitle>
            <DialogDescription className="text-blue-200 mt-2">
              Não foi possível fazer login com as credenciais fornecidas.
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-4">
              <p className="text-blue-100">
                {errorMessage}
              </p>
            </div>
            
            <div className="text-sm text-blue-300">
              <p>Sugestões:</p>
              <ul className="list-disc ml-5 mt-2 space-y-1">
                <li>Verifique se seu email está digitado corretamente</li>
                <li>Verifique se o CAPS LOCK está ativado</li>
                <li>Tente visualizar sua senha para confirmar que está correta</li>
                <li>Se o problema persistir, entre em contato com seu professor</li>
              </ul>
            </div>
          </div>
          
          <DialogFooter className="flex gap-2">
            <Button 
              onClick={() => setErrorModalOpen(false)}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Tentar novamente
            </Button>
            <Button
              onClick={handleForgotPassword}
              variant="outline"
              className="border-blue-500/30 hover:bg-blue-800/50 text-blue-300"
            >
              Esqueci minha senha
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}