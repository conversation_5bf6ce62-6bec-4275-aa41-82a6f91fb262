"use client"
import React, { useState, useEffect } from 'react'
import { Search, Filter, X, BookOpen } from 'lucide-react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import SapsAPI from '@/services/SAPS/api';

// Definição das matérias e suas olimpíadas associadas
const materias = {
  "Astronomia": ["OBA"],
  "Física": ["OBFEP"],
  "História": ["ONHB"],
  "Robótica": ["OBR"],
  "Matemática": ["OBMEP", "Canguru", "OBRL", "OMIFCE"],
  "Química": ["OBQjr"]
}

// Novas playlists organizadas por olimpíadas com IDs correspondentes ao objeto fornecido
const playlists = [
  { id: 1, title: 'OBA', description: 'Olimpíada Brasileira de Astronomia', materia: 'Astronomia', levelRange: [2,3,4,5,6, 7, 8, 9] },
  { id: 2, title: 'OBFEP', description: 'Olimpíada Brasileira de Física das Escolas Públicas', materia: 'Física', levelRange: [8, 9] },
  { id: 3, title: 'ONHB', description: 'Olimpíada Nacional em História do Brasil', materia: 'História', levelRange: [8, 9] },
  { id: 4, title: 'OBR', description: 'Olimpíada Brasileira de Robótica', materia: 'Robótica', levelRange: [2,3,4,5,6, 7, 8, 9] },
  { id: 5, title: 'OBMEP', description: 'Olimpíada Brasileira de Matemática das Escolas Públicas', materia: 'Matemática', levelRange: [2,3,4,5,6, 7, 8, 9] },
  { id: 5, title: 'Canguru', description: 'Olimpíada Internacional de Matemática Canguru', materia: 'Matemática', levelRange: [2,3,4,5,6, 7, 8, 9] },
  { id: 5, title: 'OBRL', description: 'Olimpíada Brasileira de Raciocínio Lógico', materia: 'Matemática', levelRange: [2,3,4,5,6, 7, 8, 9] },
  { id: 5, title: 'OMIFCE', description: 'Olimpíada de Matemática do Instituto Federal do Ceará', materia: 'Matemática', levelRange: [2,3,4,5,6, 7, 8, 9] },
  { id: 6, title: 'OBQjr', description: 'Olimpíada Brasileira de Química Júnior', materia: 'Química', levelRange: [8, 9] },
]

// Extrair todas as matérias únicas
const allMaterias = Object.keys(materias)

function getOlympiadInfo(olympiadId: number): { title: string, subtitle: string } | null {
  if (olympiadId === 1) {
    return { title: "OBA", subtitle: "Astronomia" }
  } else if (olympiadId === 2) {
    return { title: "OBFEP", subtitle: "Física" }
  } else if (olympiadId === 3) {
    return { title: "ONHB", subtitle: "História" }
  } else if (olympiadId === 4) {
    return { title: "OBR", subtitle: "Robótica" }
  } else if (olympiadId === 5) {
    return { title: "Matemática", subtitle: "OBMEP, Canguru, OBRL, OMIFCE" }
  } else if (olympiadId === 6) {
    return { title: "OBQjr", subtitle: "Química" }
  }

  return null
}

// Ícones personalizados para matérias
const materiaIcons: Record<string, JSX.Element> = {
  "Astronomia": <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><circle cx="12" cy="12" r="4"/><path d="M21.17 8H2.83"/><path d="M21.17 16H2.83"/><path d="M12 2v20"/></svg>,
  "Física": <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="3"/><path d="M3.34 17a10 10 0 1 1 17.32 0"/></svg>,
  "Matemática": <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polygon points="12 2 19 21 12 17 5 21 12 2"/></svg>,
  "Química": <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M8.7 3h6.6c.3 0 .5.1.7.3.2.2.3.4.3.7v5c0 .3-.1.5-.3.7l-3.6 3.6c-.2.2-.3.4-.3.7v4c0 .3-.1.5-.3.7-.2.2-.4.3-.7.3h-2.4c-.3 0-.5-.1-.7-.3-.2-.2-.3-.4-.3-.7v-4c0-.3-.1-.5-.3-.7L5.3 9.7c-.2-.2-.3-.4-.3-.7v-5c0-.3.1-.5.3-.7.2-.2.4-.3.7-.3Z"/><path d="M10 9h4"/><path d="M11 12v4"/></svg>,
  "História": <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><polyline points="12 6 12 12 16 14"/></svg>,
  "Robótica": <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="8" y="2" width="8" height="4" rx="1" ry="1"/><path d="M6 6h12v6a6 6 0 0 1-6 6 6 6 0 0 1-6-6V6z"/><path d="M6 12h12"/><path d="m8 16 4 4 4-4"/></svg>
};

export default function SpaceOlympiadCourses() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedYear, setSelectedYear] = useState<number>(8)
  const [selectedMaterias, setSelectedMaterias] = useState<string[]>([])
  const [showMateriasPopup, setShowMateriasPopup] = useState(false)
  const [activeMateria, setActiveMateria] = useState<string | null>("Matemática") // Matéria pré-selecionada
  const [isNavigating, setIsNavigating] = useState(false)

  const router = useRouter()

  useEffect(() => {
    const starContainer = document.getElementById('star-container')
    if (starContainer) {
      for (let i = 0; i < 50; i++) {
        const star = document.createElement('div')
        star.className = 'star'
        star.style.width = `${Math.random() * 2 + 1}px`
        star.style.height = star.style.width
        star.style.top = `${Math.random() * 100}%`
        star.style.left = `${Math.random() * 100}%`
        star.style.animationDuration = `${Math.random() * 3 + 2}s`
        star.style.animationDelay = `${Math.random() * 2}s`
        starContainer.appendChild(star)
      }
    }
  }, [])

  // Filtrar cursos com base em pesquisa, ano e matérias selecionadas
  const filteredCourses = playlists.filter(course =>
    (searchTerm === '' || 
     course.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
     course.description.toLowerCase().includes(searchTerm.toLowerCase())) &&
    course.levelRange.includes(selectedYear) &&
    (selectedMaterias.length === 0 || selectedMaterias.includes(course.materia)) &&
    (activeMateria === null || course.materia === activeMateria)
  )

  // Agrupar cursos por matéria
  const coursesByMateria = filteredCourses.reduce((acc, course) => {
    if (!acc[course.materia]) {
      acc[course.materia] = [];
    }
    acc[course.materia].push(course);
    return acc;
  }, {} as Record<string, typeof playlists>);

  const handleMateriaClick = (materia: string) => {
    if (activeMateria === materia) {
      setActiveMateria(null); // Desativar se clicar na mesma
    } else {
      setActiveMateria(materia); // Ativar uma nova
    }
  };

  const selectYear = (levelRange: number) => {
    setSelectedYear(levelRange);
  };

  const toggleMateria = (materia: string) => {
    setSelectedMaterias(prev =>
      prev.includes(materia) ? prev.filter(t => t !== materia) : [...prev, materia]
    );
  };

  const handleNavigationVideo = (olympiadId: number) => {
    if (isNavigating) return; // Impede múltiplos cliques

    setIsNavigating(true); // Ativa o estado de navegação
    
    const fetchPlaylistAndVideo = async () => {
      try {
        const playlist = await SapsAPI.playlistController.getFirstPlaylistByFilter({
          olympiads: [olympiadId.toString()]
        });
        
        if (playlist && playlist.id) {
          const playlistId = playlist.id;
          const videos = playlist.videos.filter(item => item.order === 1);
          
          if (videos && videos.length > 0) {
            const videoId = videos[0].videoId;
            const relativePath = `aulas/${olympiadId}/${playlistId}/${videoId}`;
            router.push(relativePath);
          } else {
            console.error("Nenhum vídeo encontrado com order = 1");
            setIsNavigating(false); // Desativa o estado de navegação em caso de erro
          }
        } else {
          console.error("Playlist não encontrada ou sem ID");
          setIsNavigating(false); // Desativa o estado de navegação em caso de erro
        }
      } catch (error) {
        console.error("Erro ao buscar playlist e vídeo:", error);
        setIsNavigating(false); // Desativa o estado de navegação em caso de erro
      }
    };

    fetchPlaylistAndVideo();
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#040A2F] to-[#0F2057] text-blue-100 py-8 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background stars */}
      <div id="star-container" className="absolute inset-0 overflow-hidden pointer-events-none" />

      <div className="max-w-7xl mx-auto relative z-10">
        <motion.h1
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-4xl font-bold mb-6 text-center text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500"
        >
          Olimpíadas Científicas
        </motion.h1>

        {/* Barra de filtros simplificada */}
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          className="mb-8 grid grid-cols-1 md:grid-cols-12 gap-4 bg-blue-900 bg-opacity-30 p-4 rounded-xl backdrop-blur-sm"
        >
          <div className="md:col-span-6 relative">
            <input
              type="text"
              placeholder="Pesquisar olimpíadas..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-blue-900 bg-opacity-50 rounded-lg py-2 px-4 pl-10 focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-blue-300"
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-300" />
          </div>

          <div className="md:col-span-4 flex gap-2 items-center">
            <span className="text-sm text-blue-300 whitespace-nowrap">Ano:</span>
            {[2,3,4,5,6, 7, 8, 9].map(levelRange => (
              <button
                key={levelRange}
                onClick={() => selectYear(levelRange)}
                className={`flex-1 px-3 py-2 rounded-lg text-sm ${selectedYear === levelRange
                  ? 'bg-blue-500 text-white'
                  : 'bg-blue-900 bg-opacity-50 text-blue-300 hover:bg-opacity-70'
                  }`}
              >
                {levelRange}º
              </button>
            ))}
          </div>

          <button
            onClick={() => setShowMateriasPopup(true)}
            className="md:col-span-2 px-4 py-2 bg-blue-600 rounded-lg flex items-center justify-center gap-2 hover:bg-blue-700 transition-colors"
          >
            <Filter size={18} />
            <span className="whitespace-nowrap">Filtros</span>
          </button>
        </motion.div>

        {/* Grid de matérias */}
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-3 mb-8"
        >
          {allMaterias.map(materia => (
            <button
              key={materia}
              onClick={() => handleMateriaClick(materia)}
              className={`p-3 rounded-lg flex flex-col items-center justify-center gap-2 transition-all ${
                activeMateria === materia
                  ? 'bg-blue-600 text-white shadow-lg shadow-blue-500/20 scale-105'
                  : 'bg-blue-900 bg-opacity-30 text-blue-200 hover:bg-opacity-50'
              }`}
            >
              <div className={`p-2 rounded-full ${
                activeMateria === materia ? 'bg-blue-500' : 'bg-blue-800 bg-opacity-50'
              }`}>
                {materiaIcons[materia]}
              </div>
              <span className="text-sm font-medium">{materia}</span>
            </button>
          ))}
        </motion.div>

        {/* Resultados - Grid de cartões */}
        <motion.div 
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-6"
        >
          {Object.keys(coursesByMateria).length > 0 ? (
            Object.entries(coursesByMateria).map(([materia, courses]) => (
              <motion.div 
                key={materia} 
                variants={itemVariants}
                className="mb-10"
              >
                <div className="flex items-center mb-4 gap-3">
                  <div className="p-2 rounded-full bg-blue-800 bg-opacity-40">
                    {materiaIcons[materia]}
                  </div>
                  <h2 className="text-2xl font-bold text-blue-100">{materia}</h2>
                  <div className="h-px flex-grow bg-blue-700 bg-opacity-30"></div>
                </div>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {courses.map(course => (
                    <motion.div 
                      key={`${course.id}-${course.title}`} 
                      onClick={() => handleNavigationVideo(course.id)}
                      className={`bg-blue-900 bg-opacity-20 rounded-lg overflow-hidden border border-blue-700 border-opacity-20 hover:border-opacity-40 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:shadow-blue-700/10 ${
                        isNavigating 
                          ? 'opacity-50 pointer-events-none' 
                          : 'cursor-pointer'
                      }`}
                      whileHover={{ scale: isNavigating ? 1 : 1.02 }}
                    >
                      <div className="p-4">
                        <h3 className="text-xl font-bold mb-2 text-blue-100">{course.title}</h3>
                        <p className="text-blue-300 mb-4 text-sm">{course.description}</p>
                        <div className="flex justify-between items-center mt-auto pt-2 border-t border-blue-700 border-opacity-20">
                          <span className="text-xs bg-blue-800 bg-opacity-40 px-2 py-1 rounded-full">
                            {course.levelRange.length === 2
                              ? `${course.levelRange[0]}º e ${course.levelRange[1]}º ano`
                              : course.levelRange.length > 1
                                ? `do ${Math.min(...course.levelRange)}º ao ${Math.max(...course.levelRange)}º ano`
                                : `${course.levelRange[0]}º ano`
                            }
                          </span>
                          {isNavigating && course.id.toString() === document.activeElement?.getAttribute('data-course-id') && (
                            <span className="text-xs text-blue-300 animate-pulse">Carregando...</span>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            ))
          ) : (
            <motion.div 
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-16 bg-blue-900 bg-opacity-20 rounded-xl"
            >
              <div className="inline-block p-5 rounded-full bg-blue-900 bg-opacity-40 mb-4">
                <Search size={40} className="text-blue-400" />
              </div>
              <p className="text-xl text-blue-300 mb-2">Nenhuma olimpíada encontrada</p>
              <p className="text-sm text-blue-400 max-w-md mx-auto">
                Tente ajustar seus filtros ou termos de pesquisa para encontrar as olimpíadas desejadas
              </p>
            </motion.div>
          )}
        </motion.div>

        {showMateriasPopup && (
          <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 backdrop-blur-sm">
            <motion.div 
              className="bg-[#0F2057] p-6 rounded-lg max-w-md w-full border border-blue-500 border-opacity-30 shadow-xl"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold">Filtrar por Matérias</h2>
                <button onClick={() => setShowMateriasPopup(false)} className="text-blue-300 hover:text-blue-100">
                  <X size={24} />
                </button>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mb-6">
                {allMaterias.map(materia => (
                  <button
                    key={materia}
                    onClick={() => toggleMateria(materia)}
                    className={`p-3 rounded-lg text-left flex items-center gap-3 transition-colors ${
                      selectedMaterias.includes(materia)
                        ? 'bg-blue-600 text-white'
                        : 'bg-blue-900 bg-opacity-50 text-blue-300 hover:bg-opacity-70'
                    }`}
                  >
                    {materiaIcons[materia]}
                    <span className="flex-grow">{materia}</span>
                    {selectedMaterias.includes(materia) && (
                      <span className="h-4 w-4 rounded-full bg-white flex items-center justify-center">
                        <span className="h-2 w-2 rounded-full bg-blue-600"></span>
                      </span>
                    )}
                  </button>
                ))}
              </div>
              <div className="flex justify-between">
                <button 
                  onClick={() => setSelectedMaterias([])} 
                  className="px-4 py-2 bg-blue-900 bg-opacity-50 rounded-lg hover:bg-opacity-70"
                >
                  Limpar
                </button>
                <button 
                  onClick={() => setShowMateriasPopup(false)} 
                  className="px-4 py-2 bg-blue-600 rounded-lg hover:bg-blue-700"
                >
                  Aplicar
                </button>
              </div>
            </motion.div>
          </div>
        )}
      </div>
    </div>
  );
}