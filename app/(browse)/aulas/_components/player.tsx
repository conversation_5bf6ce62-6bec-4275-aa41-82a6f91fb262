import React, { useEffect, useRef, useState } from 'react';

// No início do arquivo, após os imports
declare global {
    interface Window {
        pandascripttag: any[];
    }
}

interface PlayerProps {
    videoUrl: string;
    width?: number;
    height?: number;
    onWatchEvent?: () => void;
}

const PandaPlayerComponent: React.FC<PlayerProps> = ({ videoUrl, width = 720, height = 480, onWatchEvent }) => {
    const playerRef = useRef<any>(null);
    const [currentTime, setCurrentTime] = useState<number>(0);
    const [status, setStatus] = useState<string>('paused');

    // Extrai o ID do vídeo da URL
    const getVideoId = (url: string) => {
        const match = url.match(/v=([^&]+)/);
        return match ? match[1] : '';
    };

    const videoId = getVideoId(videoUrl);
    const iframeId = `panda-${videoId}`;

    useEffect(() => {
        const loadPandaScript = () => {
            const script = document.createElement('script');
            script.src = 'https://player.pandavideo.com.br/api.v2.js';
            script.async = true;
            document.body.appendChild(script);

            script.onload = () => {
                window.pandascripttag = window.pandascripttag || [];
                window.pandascripttag.push(() => {
                    const iframeElement = document.getElementById(iframeId) as HTMLIFrameElement;

                    if (iframeElement) {
                        const player = new (window as any).PandaPlayer(iframeId, {
                            onReady: () => {
                                setInterval(() => {
                                    setCurrentTime(parseFloat(player.getCurrentTime().toFixed(2)));
                                }, 1000);
                                
                                player.onEvent(({ message }: { message: string }) => {
                                    if (message === 'panda_pause') {
                                        setStatus('paused');
                                    } else if (message === 'panda_play') {
                                        setStatus('playing');
                                    } else if (message === 'panda_ended') {
                                        onWatchEvent?.();
                                    }
                                });
                            },
                            onError: (event: any) => {
                                console.error('Player error:', event);
                            },
                        });

                        playerRef.current = player;
                    }
                });
            };
        };

        loadPandaScript();

        return () => {
            if (playerRef.current) {
                clearInterval(playerRef.current);
            }
            const scriptElement = document.querySelector('script[src="https://player.pandavideo.com.br/api.v2.js"]');
            if (scriptElement) {
                document.body.removeChild(scriptElement);
            }
        };
    }, [videoId, onWatchEvent]);

    return (
        <div className="w-full aspect-w-16 aspect-h-9 rounded-lg overflow-hidden">
            <iframe
                id={iframeId}
                src={videoUrl}
                style={{ border: 'none' }}
                allow="accelerometer;gyroscope;autoplay;encrypted-media;picture-in-picture"
                allowFullScreen={true}
                className="w-full h-full"
            />
        </div>
    );
};

export default PandaPlayerComponent;
