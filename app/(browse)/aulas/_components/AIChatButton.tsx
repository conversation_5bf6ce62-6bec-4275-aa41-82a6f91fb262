'use client'

import { motion } from 'framer-motion'
import { Brain } from 'lucide-react'

export default function AIChatButton() {
  return (
    <motion.div
      className="fixed bottom-4 right-4 z-50"
      whileHover={{ scale: 1.05 }}
    >
      <div
        className="bg-blue-600/50 text-white font-bold py-3 px-6 rounded-full shadow-lg transition-all duration-300 ease-in-out flex items-center opacity-75"
      >
        <Brain className="w-6 h-6 mr-2" />
        IA tira dúvidas em breve
      </div>
      <motion.div
        className="absolute inset-0 rounded-full pointer-events-none"
        animate={{
          boxShadow: [
            "0 0 0 0px rgba(59, 130, 246, 0.5)",
            "0 0 0 4px rgba(59, 130, 246, 0.5)", 
            "0 0 0 0px rgba(59, 130, 246, 0.5)",
          ],
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          repeatType: "loop",
        }}
      />
    </motion.div>
  )
}