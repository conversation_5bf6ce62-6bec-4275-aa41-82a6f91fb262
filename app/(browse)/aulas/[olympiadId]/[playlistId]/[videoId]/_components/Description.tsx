// src/app/(browse)/aulas/_components/Description.tsx
import React from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface DescriptionProps {
  description: string;
  isLong: boolean;
  expanded: boolean;
  toggleExpanded: () => void;
}

const Description: React.FC<DescriptionProps> = ({ description, isLong, expanded, toggleExpanded }) => {
  return (
    <div className="bg-blue-900 bg-opacity-30 rounded-lg p-4 mb-8">
      <h3 className="text-xl font-bold mb-3 flex items-center">
        {/* Ícone de descrição */}
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        Des<PERSON><PERSON><PERSON> da Aula
      </h3>
      <p className={`${expanded || !isLong ? '' : 'line-clamp-2'}`}>
        {description}
      </p>
      {isLong && (
        <button
          onClick={toggleExpanded}
          className="text-blue-400 hover:text-blue-300 mt-2 flex items-center"
        >
          {expanded ? (
            <>
              <span>Mostrar menos</span>
              <ChevronUp size={18} className="ml-1" />
            </>
          ) : (
            <>
              <span>Mostrar mais</span>
              <ChevronDown size={18} className="ml-1" />
            </>
          )}
        </button>
      )}
    </div>
  );
}

export default Description;
