import React from 'react';
import { Star } from '../page'; // Ajuste o caminho conforme necessário

interface StarBackgroundProps {
  stars: Star[];
}

const StarBackground: React.FC<StarBackgroundProps> = ({ stars }) => {
  return (
    <div className="absolute inset-0 pointer-events-none z-0">
      {stars.map((star) => (
        <div
          key={star.id}
          className="absolute rounded-full bg-white"
          style={{
            top: star.top,
            left: star.left,
            width: star.width,
            height: star.width,
            animation: `twinkle ${star.animationDuration} infinite ease-in-out`,
          }}
        />
      ))}
      <style jsx global>{`
        @keyframes twinkle {
          0%, 100% { opacity: 0.3; transform: scale(0.8); }
          50% { opacity: 1; transform: scale(1); }
        }
      `}</style>
    </div>
  );
}

export default StarBackground;
