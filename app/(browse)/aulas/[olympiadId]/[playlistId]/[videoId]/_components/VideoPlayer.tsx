import React, { RefObject , use} from 'react';
import PandaPlayerComponent from '@/components/videos/player';

interface VideoPlayerProps {
  videoUrl: string | undefined;
  onWatchEvent: (percentage: number) => void;
  onReadyEvent: () => void;
  width: number;
  height: number;
  containerRef: RefObject<HTMLDivElement>;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ videoUrl, onWatchEvent, onReadyEvent, width, height, containerRef }) => {
  

  return (
    <div className="aspect-w-16 aspect-h-9 mb-4 cursor-pointer">
      <div ref={containerRef} 
            className="video-container w-full"
      >
        <PandaPlayerComponent 
          videoUrl={videoUrl}
          onWatchEvent={onWatchEvent}
          onReadyEvent={onReadyEvent}
          width={width}
          height={height}
        />
      </div>
    </div>
  );
}



export default VideoPlayer;
