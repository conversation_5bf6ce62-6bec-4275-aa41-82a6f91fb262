// src/app/(browse)/aulas/_components/Attachments.tsx
import React from 'react';
import { motion } from 'framer-motion';

interface AttachmentsProps {
  videoFiles: string[];
}

const Attachments: React.FC<AttachmentsProps> = ({ videoFiles }) => {
  if (!videoFiles || videoFiles.length === 0) return null;

  return (
    <div className="bg-blue-900 bg-opacity-30 rounded-lg p-4 mb-4">
      <h3 className="text-xl font-bold mb-3 flex items-center">
        {/* Ícone de anexos */}
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
        </svg>
        Anexos da Aula
      </h3>
      {/* <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {videoFiles.map((attachment) => (
          <motion.a
            key={attachment.id}
            href={attachment.videoUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center p-3 bg-blue-800 bg-opacity-50 rounded-lg hover:bg-opacity-70 transition-all duration-300"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {attachment.type === 'pdf' && (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            )}
            {attachment.type === 'doc' && (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            )}
            {attachment.type === 'image' && (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            )}
            <span className="text-sm">{attachment.name}</span>
          </motion.a>
        ))}
      </div> */}
    </div>
  );
}

export default Attachments;
