// src/app/(browse)/aulas/_components/Comment.tsx
import React from 'react';
import Image, { StaticImageData } from 'next/image';
import { ThumbsUp } from 'lucide-react';
import ReplyForm from './ReplyForm'
import { IComment } from '@/services/SAPS/interfaces';

// Utility function to format relative time
const formatRelativeTime = (date: Date): string => {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - new Date(date).getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'agora mesmo';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `há ${diffInMinutes} ${diffInMinutes === 1 ? 'minuto' : 'minutos'}`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `há ${diffInHours} ${diffInHours === 1 ? 'hora' : 'horas'}`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `há ${diffInDays} ${diffInDays === 1 ? 'dia' : 'dias'}`;
  }

  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) {
    return `há ${diffInWeeks} ${diffInWeeks === 1 ? 'semana' : 'semanas'}`;
  }

  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return `há ${diffInMonths} ${diffInMonths === 1 ? 'mês' : 'meses'}`;
  }

  const diffInYears = Math.floor(diffInDays / 365);
  return `há ${diffInYears} ${diffInYears === 1 ? 'ano' : 'anos'}`;
};

interface CommentProps {
  comment: IComment;
  handleLike: (commentId: number) => void;
  replyStates: {
    [commentId: number]: {
      isReplying: boolean;
      replyContent: string;
    }
  };
  toggleReply: (commentId: number) => void;
  addComment: (parentId?: number | undefined ) => Promise<void>;
  updateReplyContent: (commentId: number, content: string) => void;
  avatarImage: StaticImageData;
  commentNestingLevels: { [key: number]: number };
  userId: string;
}

const Comment: React.FC<CommentProps> = ({
  comment,
  handleLike,
  replyStates,
  toggleReply,
  addComment,
  updateReplyContent,
  avatarImage,
  commentNestingLevels,
  userId
}) => {
  const nestingLevel = commentNestingLevels[comment.id] || 0;
  const canReply = nestingLevel < 2;
  const hasUserLiked = comment.likes?.some(like => like.userId === userId);

  return (
    <div className="flex space-x-4">
      <div className="flex-shrink-0 w-8 h-8 mb-1">
        <Image
          src={avatarImage}
          alt="User Avatar"  
          width={32}
          height={32}
          className="rounded-full object-cover w-full h-full"
        />
      </div>
      <div className="flex-grow">
        <div className="flex items-center mb-1">
          <span className="font-semibold mr-2">{comment.username}</span>
          <span className="text-sm text-blue-300">{formatRelativeTime(comment.createdAt)}</span>
        </div>
        <p>{comment.content}</p>

        <div className="flex items-center space-x-4 mt-2">
          {/* <button
            onClick={() => handleLike(comment.id)}
            className={`flex items-center ${hasUserLiked ? 'text-blue-400' : 'text-blue-300'} hover:text-blue-100`}
          >
            <ThumbsUp size={16} className={`mr-1 ${hasUserLiked ? 'fill-current' : ''}`} />
            {comment.likes?.length || 0}
          </button> */}
          {canReply && (
            <button
              onClick={() => toggleReply(comment.id)}
              className="text-blue-300 hover:text-blue-100"
            >
              Responder
            </button>
          )}
        </div>

        {replyStates[comment.id]?.isReplying && (
          <ReplyForm 
            commentId={comment.id}
            replyContent={replyStates[comment.id]?.replyContent}
            updateReplyContent={updateReplyContent}
            addComment={addComment}
            toggleReply={toggleReply}
            avatarImage={avatarImage}
          />
        )}

        {comment.responseComments && comment.responseComments.length > 0 && (
          <div className="mt-4 ml-8 space-y-4">
            {comment.responseComments.map(reply => (
              <Comment 
                key={reply.id}
                comment={reply}
                handleLike={handleLike}
                replyStates={replyStates}
                toggleReply={toggleReply}
                addComment={addComment}
                updateReplyContent={updateReplyContent}
                avatarImage={avatarImage}
                commentNestingLevels={commentNestingLevels}
                userId={userId}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
export default Comment;

