 // src/app/(browse)/aulas/_components/VideoInfo.tsx
import React from 'react';
import Image from 'next/image';
import { ThumbsUp, Bookmark, Eye } from 'lucide-react';
import AIChatButton from '@/app/(browse)/aulas/_components/AIChatButton';
import { IUserVideoStatistics, IVideoStatistics } from '@/services/SAPS/interfaces';
import avatarImage from '@/assets/avatar.png';

interface Author {
  name: string;
  followers: number;
  avatar: string;
  description: string;
}

interface InteractionInfo {
  user: IUserVideoStatistics;
  video: IVideoStatistics;
}

interface VideoInfoProps {
  title: string;
  author: Author;
  interactionInfo: InteractionInfo;
  onLike: () => void;
  onSave: () => void;
}

const getLikeButtonStyle = (hasUserLiked: boolean): string => {
  if (hasUserLiked)
    return `flex items-center space-x-1 bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-full`;
  return `flex items-center space-x-1 bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-full`;
}

const getSaveButtonStyle = (hasUserSaved: boolean): string => {
  if (hasUserSaved)
    return "flex items-center space-x-1 bg-green-800 bg-opacity-50 hover:bg-opacity-75 px-4 py-2 rounded-full";
  return "flex items-center space-x-1 bg-blue-800 bg-opacity-50 hover:bg-opacity-75 px-4 py-2 rounded-full";
}

function getWatchedButtonStyle(hasUserWatched: boolean): string {
  if (hasUserWatched)
    return "flex items-center space-x-1 bg-green-800 bg-opacity-50 hover:bg-opacity-75 px-4 py-2 rounded-full"
  return "flex items-center space-x-1 bg-blue-800 bg-opacity-50 hover:bg-opacity-75 px-4 py-2 rounded-full"
}


const VideoInfo: React.FC<VideoInfoProps> = ({ title, author, interactionInfo, onLike, onSave }) => {
  return (
    <>
      <h1 className="text-2xl font-bold mb-2">{title}</h1>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-2 sm:space-y-0 mb-4">
        <div className="flex items-start space-x-4">
          <Image
            src={author.avatar || avatarImage}
            alt="Channel Avatar"
            width={40}
            height={40}
            className="rounded-full"
          />
          <div>
            <h2 className="font-semibold">{author.name}</h2>
            {author.description && (
              <p className="text-sm text-blue-200 mt-1 max-w-xl">{author.description}</p>
            )}
          </div>
        </div>
        <div className="flex flex-wrap gap-2">
          <button 
            className={getLikeButtonStyle(interactionInfo.user.hasUserLiked)} 
            onClick={onLike}
          >
            {interactionInfo.user.hasUserLiked ? (
              <ThumbsUp size={18} className="text-white fill-current" />
            ) : (
              <ThumbsUp size={18} />
            )}  
            <span>Gostei</span>
          </button>
          <AIChatButton />
          <button 
            className={getSaveButtonStyle(interactionInfo.user.hasUserSaved)} 
            onClick={onSave}
          >
            {interactionInfo.user.hasUserSaved ? (
              <Bookmark size={18} className="text-white fill-current" />
            ) : (
              <Bookmark size={18} />
            )}
            <span>Salvar</span>
          </button>
          <button
            className={getWatchedButtonStyle(interactionInfo.user.hasUserWatched)} 
          >
            {interactionInfo.user.hasUserWatched ? (
              <Eye size={18} className="text-white fill-current" />
            ) : (
              <Eye size={18} />
            )}  
            <span>Assistido</span>
          </button>
        </div>
      </div>
    </>
  );
}

export default VideoInfo;
