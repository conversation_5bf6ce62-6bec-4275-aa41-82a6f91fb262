// src/app/(browse)/aulas/_components/ReplyForm.tsx
import React from 'react';
import avatarImage from '@/assets/avatar.png';
import Image, { StaticImageData } from 'next/image';

interface ReplyFormProps {
  commentId: number;
  replyContent: string;
  updateReplyContent: (commentId: number, content: string) => void;
  addComment: (parentId?: number | undefined ) => Promise<void>;
  toggleReply: (commentId: number) => void;
  avatarImage: StaticImageData;
}

const ReplyForm: React.FC<ReplyFormProps> = ({
  commentId,
  replyContent,
  updateReplyContent,
  addComment,
  toggleReply,
  avatarImage
}) => {
  const handleSubmit = async () => {
    if (replyContent.trim()) {
      await addComment(commentId);
      toggleReply(commentId);
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  return (
    <div className="mt-2 flex items-center space-x-4">
      <Image
        src={avatarImage}
        alt="Your Avatar"
        width={32}
        height={32}
        className="rounded-full"
      />
      <input
        type="text"
        value={replyContent || ''}
        onChange={(e) => updateReplyContent(commentId, e.target.value)}
        onKeyPress={handleKeyPress}
        placeholder="Adicione uma resposta..."
        className="flex-grow bg-blue-800 bg-opacity-50 rounded-full py-1 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
      <button
        onClick={handleSubmit}
        className="bg-blue-600 hover:bg-blue-700 rounded-full px-3 py-1 text-sm"
      >
        Responder
      </button>
    </div>
  );
}
export default ReplyForm;

