// src/app/(browse)/aulas/_components/CommentsSection.tsx
import React, { useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Avatar } from '@/components/ui/avatar';
import { IComment } from '@/services/SAPS/interfaces';
import moment from 'moment';

interface Props {
  comments: IComment[];
  addComment: (parentId?: number, content?: string, isDoubt?: boolean) => Promise<void>;
  handleLike: (commentId: number) => void;
  replyStates: {
    [commentId: number]: {
      isReplying: boolean;
      replyContent: string;
    }
  };
  toggleReply: (commentId: number) => void;
  updateReplyContent: (commentId: number, content: string) => void;
  avatarImage: string;
  commentNestingLevels: { [key: number]: number };
  userId: string;
}

const CommentsSection: React.FC<Props> = ({
  comments,
  addComment,
  handleLike,
  replyStates,
  toggleReply,
  updateReplyContent,
  avatarImage,
  commentNestingLevels,
  userId
}) => {
  const [newCommentContent, setNewCommentContent] = useState('');
  const [isDoubt, setIsDoubt] = useState(false);

  const submitNewComment = () => {
    if (newCommentContent.trim()) {
      // Adiciona o campo isDoubt ao criar comentário
      addComment(undefined, newCommentContent, isDoubt);
      setNewCommentContent('');
      setIsDoubt(false);
    }
  };

  // Função auxiliar para formatar a data do comentário
  const formatCommentDate = (date: Date) => {
    return moment(date).fromNow();
  };

  // Função recursiva para renderizar comentários e suas respostas
  const renderComment = (comment: IComment, nestingLevel: number = 0) => {
    const isUserLiked = comment.likes?.some(like => like.userId === userId) || false;
    const canReply = nestingLevel < 2; // Limita a 3 níveis de comentários (0, 1, 2)
    
    return (
      <div 
        key={comment.id} 
        className={`bg-[#0A1340] rounded-lg p-3 mb-4 ${nestingLevel > 0 ? `ml-${nestingLevel * 4}` : ''}`}
        style={{ marginLeft: nestingLevel > 0 ? `${nestingLevel * 1.5}rem` : '0' }}
      >
        <div className="flex items-start space-x-3">
          <Avatar className="h-8 w-8 rounded-full">
            <Image src={avatarImage} alt="User" layout="fill" className="rounded-full" />
          </Avatar>
          <div className="flex-1 min-w-0">
            <div className="flex flex-wrap justify-between items-center mb-1">
              <span className="font-semibold text-sm">{comment.username || 'Usuário'}</span>
              <span className="text-xs text-blue-300">{formatCommentDate(new Date(comment.createdAt))}</span>
              {comment.isDoubt && (
                <span className="ml-2 px-2 py-0.5 rounded bg-yellow-400 text-yellow-900 text-xs font-bold">DÚVIDA</span>
              )}
            </div>
            <p className="text-sm mb-2 break-words">{comment.content}</p>
            <div className="flex flex-wrap gap-3 items-center">
              <button
                onClick={() => handleLike(comment.id)}
                className={`text-xs flex items-center ${isUserLiked ? 'text-blue-400' : 'text-blue-300'}`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z" />
                </svg>
                {comment.likes?.length || 0}
              </button>
              {canReply && (
                <button
                  onClick={() => toggleReply(comment.id)}
                  className="text-xs text-blue-300 hover:text-blue-200"
                >
                  Responder
                </button>
              )}
            </div>

            {/* Campo de resposta ao comentário */}
            {replyStates[comment.id]?.isReplying && (
              <div className="mt-3 flex flex-col space-y-2">
                <Textarea
                  value={replyStates[comment.id]?.replyContent || ''}
                  onChange={(e) => updateReplyContent(comment.id, e.target.value)}
                  placeholder="Escreva uma resposta..."
                  className="min-h-[80px] bg-[#061037] border-blue-900 text-sm"
                />
                <div className="flex flex-wrap gap-2 justify-end">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleReply(comment.id)}
                    className="text-blue-300 hover:text-blue-100 hover:bg-blue-900/50"
                  >
                    Cancelar
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => addComment(comment.id)}
                    disabled={!replyStates[comment.id]?.replyContent?.trim()}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Responder
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Renderiza respostas recursivamente */}
        {comment.responseComments && comment.responseComments.length > 0 && (
          <div className="mt-3">
            {comment.responseComments.map(response => renderComment(response, nestingLevel + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="mt-8 pb-8">
      <h3 className="text-xl font-semibold mb-4">Comentários</h3>
      
      {/* Seção para adicionar novo comentário */}
      <div className="mb-6 bg-[#0A1340] rounded-lg p-4">
        <div className="flex flex-col sm:flex-row items-start gap-3">
          <Avatar className="h-10 w-10 rounded-full hidden sm:block">
            <Image src={avatarImage} alt="User" layout="fill" className="rounded-full" />
          </Avatar>
          <div className="flex-1 w-full">
            <Textarea
              value={newCommentContent}
              onChange={(e) => setNewCommentContent(e.target.value)}
              placeholder="Adicione um comentário..."
              className="min-h-[100px] bg-[#061037] border-blue-900 mb-3"
            />
            <div className="flex items-center justify-between mb-2">
              <label className="flex items-center gap-2 text-blue-300 text-sm">
                <input
                  type="checkbox"
                  checked={isDoubt}
                  onChange={e => setIsDoubt(e.target.checked)}
                  className="accent-blue-600 h-4 w-4"
                />
                Marcar como dúvida
              </label>
            </div>
            <div className="flex justify-end">
              <Button
                onClick={submitNewComment}
                disabled={!newCommentContent.trim()}
                className="bg-blue-600 hover:bg-blue-700 px-4 py-2"
              >
                Comentar
              </Button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Lista de comentários */}
      <div>
        {comments.length > 0 ? (
          comments.map(comment => renderComment(comment, commentNestingLevels[comment.id] || 0))
        ) : (
          <p className="text-center text-blue-300">Nenhum comentário ainda. Seja o primeiro a comentar!</p>
        )}
      </div>
    </div>
  );
};

export default CommentsSection;
