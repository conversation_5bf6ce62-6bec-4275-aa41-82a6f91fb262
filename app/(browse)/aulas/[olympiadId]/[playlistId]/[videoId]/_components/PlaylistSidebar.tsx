// src/app/(browse)/aulas/_components/PlaylistsSidebar.tsx
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import PlaylistItem from './PlaylistItem'
import { IPlaylist } from '@/services/SAPS/interfaces';
import { Plus } from 'lucide-react';


interface PlaylistsSidebarProps {
  playlists: IPlaylist[];
  expandedPlaylists: number[];
  togglePlaylist: (playlistId: number) => void;
  handleVideoChange: (newVideoId: number) => void;
  currentPlaylistId: number;
  currentVideoId: number;
}

const PLAYLISTS_PER_PAGE = 3;


const PlaylistsSidebar: React.FC<PlaylistsSidebarProps> = ({
  playlists,
  expandedPlaylists,
  togglePlaylist,
  handleVideoChange,
  currentPlaylistId,
  currentVideoId
}) => {
  const [visiblePlaylists, setVisiblePlaylists] = useState<number>(PLAYLISTS_PER_PAGE);
  const hasMorePlaylists = visiblePlaylists < playlists.length;

  const loadMorePlaylists = () => {
    setVisiblePlaylists(prev => prev + PLAYLISTS_PER_PAGE);
  };
  return (
    <div className="sticky top-8">
      <motion.h3
        className="text-2xl font-bold mb-6 text-center"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        Módulos Espaciais
      </motion.h3>
      <motion.div
        className="space-y-4 max-h-[calc(100vh-12rem)] overflow-y-auto pr-2 custom-scrollbar"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <style jsx global>{`
          .custom-scrollbar::-webkit-scrollbar {
            width: 8px;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: rgba(30, 58, 138, 0.3);
            border-radius: 4px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(37, 99, 235, 0.5);
            border-radius: 4px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(37, 99, 235, 0.7);
          }
        `}</style>
        <AnimatePresence>
        {playlists?.slice(0, visiblePlaylists).map(playlist => (
            <PlaylistItem 
              key={playlist.id}
              playlist={playlist}
              isExpanded={expandedPlaylists.includes(playlist.id)}
              togglePlaylist={() => togglePlaylist(playlist.id)}
              handleVideoChange={handleVideoChange}
              isActive={playlist.id === currentPlaylistId}
              currentVideoId={currentVideoId}
            />
          ))}
        </AnimatePresence>

        {hasMorePlaylists && (
          <motion.button
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={loadMorePlaylists}
            className="w-full mt-4 py-2 px-4 bg-blue-800 bg-opacity-50 hover:bg-opacity-75 rounded-lg transition-all duration-200 flex items-center justify-center"
          >
            <Plus size={20} className="mr-2" />
            Mostrar mais {Math.min(PLAYLISTS_PER_PAGE, playlists.length - visiblePlaylists)} módulos
          </motion.button>
        )}
      </motion.div>
    </div>
  );
}

export default PlaylistsSidebar;
