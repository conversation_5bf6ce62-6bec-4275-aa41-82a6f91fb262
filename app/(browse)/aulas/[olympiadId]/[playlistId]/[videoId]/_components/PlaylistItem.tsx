// src/app/(browse)/aulas/_components/PlaylistItem.tsx
import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, Play, Rocket } from 'lucide-react';
import { IPlaylist } from '@/services/SAPS/interfaces';

interface PlaylistItemProps {
  playlist: IPlaylist;
  isExpanded: boolean;
  togglePlaylist: () => void;
  handleVideoChange: (newVideoId: number) => void;
  isActive: boolean;
  currentVideoId: number;
}

const PlaylistItem: React.FC<PlaylistItemProps> = ({
  playlist,
  isExpanded,
  togglePlaylist,
  handleVideoChange,
  isActive,
  currentVideoId
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      transition={{ duration: 0.3 }}
    >
      <div className={`bg-blue-900 bg-opacity-30 rounded-lg overflow-hidden mb-4 ${isActive ? 'ring-2 ring-blue-400 ring-opacity-30' : ''}`}>
        <motion.button
          onClick={togglePlaylist}
          className="w-full flex justify-between items-center p-4 hover:bg-blue-800 hover:bg-opacity-50 transition-colors"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <span className="font-semibold flex items-center">
            <Rocket className="mr-2" size={18} />
            {playlist.title}
          </span>
          <motion.div
            animate={{ rotate: isExpanded ? 180 : 0 }}
            transition={{ duration: 0.3 }}
          >
            <ChevronDown size={18} />
          </motion.div>
        </motion.button>
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="p-2 space-y-2">
                {playlist.videos.map(playlistVideo => (
                  <motion.div
                    key={playlistVideo.videoId}
                    onClick={() => handleVideoChange(playlistVideo.videoId)}
                    className={`flex items-center space-x-2 p-2 rounded-lg cursor-pointer transition-colors ${currentVideoId === playlistVideo.videoId
                      ? 'bg-blue-600 bg-opacity-50'
                      : 'hover:bg-blue-800 hover:bg-opacity-50'
                      }`}
                    whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <div className="relative w-20 h-12 flex-shrink-0">
                      {/* Thumbnail pode ser adicionado aqui */}
                      {/* Exemplo comentado */}
                      {/* <Image
                        src={video.thumbnail}
                        alt={video.title}
                        width={80}
                        height={45}
                        className="w-full h-full object-cover rounded"
                      /> */}
                      <motion.div
                        className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded"
                        whileHover={{ backgroundColor: 'rgba(0,0,0,0.7)' }}
                      >
                        <Play size={24} className="text-white" />
                      </motion.div>
                    </div>
                    <div className="flex-grow min-w-0">
                      <h4 className="font-semibold text-sm line-clamp-2">{playlistVideo.video?.title}</h4>
                      {currentVideoId === playlistVideo.videoId && (
                        <motion.div
                          initial={{ opacity: 0, scale: 0.5 }}
                          animate={{ opacity: 1, scale: 1 }}
                          className="text-xs text-blue-300 mt-1"
                        >
                          Assistindo agora
                        </motion.div>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
}

export default PlaylistItem;
