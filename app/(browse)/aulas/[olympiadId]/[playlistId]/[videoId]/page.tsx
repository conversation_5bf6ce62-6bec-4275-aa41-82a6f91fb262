'use client'

import React, { useState, useEffect, useCallback, useRef, useLayoutEffect, useMemo } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

import avatarImage from '@/assets/avatar.png';
import SapsAPI from '@/services/SAPS/api';
import { Video } from '../../../../../../data/videoData';
import { useUserInformation } from "@/context/UserContext";
import { IVideoStatistics, IUserVideoStatistics, IPlaylist, IComment, ICommentLike } from '@/services/SAPS/interfaces';

import StarBackground from './_components/StarBackground';
import VideoPlayer from './_components/VideoPlayer';
import VideoInfo from './_components/VideoInfo';
import Attachments from './_components/Attachments';
import Description from './_components/Description';
import PlaylistsSidebar from './_components/PlaylistSidebar';
import CommentsSection from './_components/CommentsSection';

// Interfaces e Types
export interface Star {
  id: number;
  top: string;
  left: string;
  width: string;
  animationDuration: string;
}

interface CommentsSectionProps {
  comments: IComment[];
  addComment: (parentId?: number | undefined, content?: string) => Promise<void>;
  handleLike: (commentId: number) => void;
  replyStates: {
    [commentId: number]: {
      isReplying: boolean;
      replyContent: string;
    }
  };
  toggleReply: (commentId: number) => void;
  updateReplyContent: (commentId: number, content: string) => void;
  avatarImage: string;
  commentNestingLevels: { [key: number]: number };
  userId: string;
}

// Funções Utilitárias
const createStars = () => {
  const stars = []
  for (let i = 0; i < 100; i++) {
    stars.push({
      id: i,
      top: `${Math.random() * 100}%`,
      left: `${Math.random() * 100}%`,
      width: `${Math.random() * 2 + 1}px`,
      animationDuration: `${Math.random() * 4 + 2}s`,
    })
  }
  return stars
}


export default function SpaceVideoLesson() {
  const { user } = useUserInformation();
  const router = useRouter();
  const params = useParams();

  // Parâmetros da URL e id global
  const userId = user?._id || undefined;
  const olympiadId = params?.olympiadId as string;
  const playlistId = params?.playlistId as string;
  const videoId = params?.videoId as string;
 
  // Estados de Dados Principais
  const [playlists, setPlaylists] = useState<IPlaylist[]>([]);
  const [currentPlaylist, setCurrentPlaylist] = useState<IPlaylist | null>(null);
  const [currentVideo, setCurrentVideo] = useState<Video | null>(null);
  const [comments, setComments] = useState<IComment[]>([]);
  const [localComments, setLocalComments] = useState<IComment[]>([]);
  const [interactionInformation, setInteractionInformation] = useState<{
    user: IUserVideoStatistics,
    video: IVideoStatistics
  }>({
    user: { hasUserLiked: false, hasUserSaved: false, hasUserWatched: false, userLikedCommentsIds: [] },
    video: { numberOfComments: 0, numberOfLikes: 0, numberOfSaves: 0 }
  });

  // Estados de UI
  const [expandedDescription, setExpandedDescription] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [expandedPlaylists, setExpandedPlaylists] = useState<number[]>([]);
  const [isDescriptionLong, setIsDescriptionLong] = useState(false);
  const [stars] = useState(createStars());

  // Estados de Comentários
  const [replyStates, setReplyStates] = useState<{
    [commentId: number]: {
      isReplying: boolean;
      replyContent: string;
    }
  }>({});

  // Referencia  e Estados do Player
  const videoContainerRef = useRef<HTMLDivElement | null>(null);
  const [playerDimensions, setPlayerDimensions] = useState({ width: 800, height: 450 });

  const [hasMarkedAsWatched, setHasMarkedAsWatched] = useState(false);
  const [hasLoadedFirstTime, setHasLoadedFirstTime] = useState(false); // para controlar o primeiro resize

  /************************* Fetchs de Dados: Playlist e Video com devidas estatísticas baseadas no id do usuário*******************************/
  useEffect(() => {
    let isSubscribed = true;

    const fetchPlaylistAndVideo = async () => {
      try {
        const playlist = await SapsAPI.playlistController.getPlaylistById({ playlistId: Number(playlistId) })

        if (!isSubscribed) return;
        setCurrentPlaylist(playlist)

        if (playlist && userId) {
          const { video, statistics, userStatistics } = await SapsAPI.videoController.getVideoWithStatistics({ userId, videoId: Number(videoId), playlistId: Number(playlistId) })

          if (!isSubscribed) return;
          setInteractionInformation({ user: userStatistics, video: statistics })
          setCurrentVideo(video)

          // Organiza os comentários hierarquicamente
          const organizeComments = (comments: IComment[]) => {
            // Cria um Map para acesso rápido aos comentários por ID
            const commentMap = new Map<number, IComment>(comments.map(c => [c.id, { 
              ...c, 
              responseComments: [],
              username: c.username || "Usuário Anônimo" // Fallback to userId if username is not available
            }]));

            // Array para armazenar apenas comentários principais
            const rootComments: IComment[] = [];

            // Organiza os comentários em sua hierarquia
            comments.forEach(comment => {
              if (comment.parentCommentId === null) {
                rootComments.push(commentMap.get(comment.id)!);
              } else {
                const parentComment = commentMap.get(comment.parentCommentId!);
                if (parentComment) {
                  if (!parentComment.responseComments) {
                    parentComment.responseComments = [];
                  }
                  const childComment = commentMap.get(comment.id);
                  if (childComment) {
                    parentComment.responseComments.push(childComment);
                  }
                }
              }
            });

            return rootComments;
          };

          setComments(organizeComments(video.comments));
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }

    if (playlistId && videoId && userId) {
      fetchPlaylistAndVideo()
    }

    return () => {
      isSubscribed = false;
    }
  }, [playlistId, videoId, userId])

  /************************* Fetchs de Dados: Todas as Playlists que ficarão na lateral da tela *************************/
  useEffect(() => {
    let isSubscribed = true;

    const getAllPlaylists = async () => {
      try {
        const playlists = await SapsAPI.playlistController.getPlaylistByType({
          page: 1,
          limit: 1000,
          subject: null,
          type: 'GLOBAL'
        })

        if (!isSubscribed) return;
        setPlaylists(playlists)
      } catch (error) {
        console.error("Error fetching playlists:", error);
      }
    }

    getAllPlaylists()

    return () => {
      isSubscribed = false;
    }
  }, [])

  /*********************** Handlers do Player: organiza as dimensões do player **********************************/
  const updatePlayerDimensions = useCallback(() => {
    const container = videoContainerRef.current;
    if (container) {
      const parentWidth = container.clientWidth || 800;
      // Mantém a proporção 16:9 para o vídeo
      const height = Math.floor((parentWidth * 9) / 16);
      setPlayerDimensions({ width: parentWidth, height });
    }
  }, []);

  // Adicionar um useEffect para lidar com redimensionamento
  useEffect(() => {
    window.addEventListener('resize', updatePlayerDimensions);
    // Pequeno atraso para garantir que os elementos DOM estejam prontos
    const timer = setTimeout(() => {
      updatePlayerDimensions();
    }, 100);

    return () => {
      window.removeEventListener('resize', updatePlayerDimensions);
      clearTimeout(timer);
    };
  }, [updatePlayerDimensions]);

  const handleVideoLoaded = () => {
    updatePlayerDimensions();
    setHasLoadedFirstTime(true);
  };



  /************************* Handlers de Player: muda o video atual caso clique em outra playlist *************************/
  const handleVideoChange = (newVideoId: number) => {
    if (currentPlaylist) {
      router.push(`/aulas/${olympiadId}/${currentPlaylist.id}/${newVideoId}`)
    }
  }

  const togglePlaylist = (playlistId: number) => {
    setExpandedPlaylists(prev =>
      prev.includes(playlistId)
        ? prev.filter(id => id !== playlistId)
        : [...prev, playlistId]
    )
  }


  /************************* Handlers de Interação: gerencia as interações do usuário no vídeo (like, save, watched) *************************/


  async function handleClickLikeButton() {
    if (userId && videoId && playlistId) {
      const userHasLiked = await SapsAPI.videoController.addOrRemoveLikeToVideo({
        userId: userId as string,
        videoId: Number(videoId),
        playlistId: Number(playlistId)
      });


      setInteractionInformation(prev => ({
        ...prev,
        user: { ...prev.user, hasUserLiked: userHasLiked }
      }))

    }
  }

  async function handleClickSaveButton() {
    const userHasSaved = await SapsAPI.videoController.addOrRemoveSaveToVideo({
      userId: userId as string,
      videoId: Number(videoId),
    });


    setInteractionInformation(prev => ({
      ...prev,
      user: { ...prev.user, hasUserSaved: userHasSaved }
    }))


  }

  const memoizedHandleVideoWatched = (percentage: number) => {
    if (!hasMarkedAsWatched) {
      setHasMarkedAsWatched(true);
      handleWatched();
    }
  };

  async function handleWatched() {
    const userHasWatched = await SapsAPI.videoController.addOrRemoveWatchedToVideo({
      userId: userId as string,
      videoId: Number(videoId),
      playlistId: Number(playlistId)
    });

    setInteractionInformation(prev => ({
      ...prev,
      user: { ...prev.user, hasUserWatched: userHasWatched }
    }));
  }

  /************************* Handlers de Comentários: gerencia o estado de resposta de um comentário *************************/
  const toggleReplyTo = (commentId: number) => {
    setReplyStates(prev => {
      const isCurrentlyReplying = prev[commentId]?.isReplying ?? false;
      const resetStates = Object.keys(prev).reduce((acc: any, key: any) => ({
        ...acc,
        [key]: {
          isReplying: false,
          replyContent: prev[key]?.replyContent ?? ''
        }
      }), {});

      return isCurrentlyReplying ? resetStates : {
        ...resetStates,
        [commentId]: {
          isReplying: true,
          replyContent: prev[commentId]?.replyContent ?? ''
        }
      };
    });
  };

  const updateReplyContent = (commentId: number, content: string) => {
    setReplyStates(prev => ({
      ...prev,
      [commentId]: {
        ...prev[commentId],
        replyContent: content
      }
    }));
  };

  // Function to check nesting level of a comment
  const getCommentNestingLevel = (comments: IComment[], targetId: number, currentLevel: number = 0): number => {
    for (const comment of comments) {
      if (comment.id === targetId) {
        return currentLevel;
      }
      if (comment.responseComments?.length) {
        const level = getCommentNestingLevel(comment.responseComments, targetId, currentLevel + 1);
        if (level !== -1) return level;
      }
    }
    return -1;
  };

  // Function to get nesting levels for all comments
  const getCommentNestingLevels = (comments: IComment[], currentLevel: number = 0): { [key: number]: number } => {
    const levels: { [key: number]: number } = {};
    
    comments.forEach(comment => {
      levels[comment.id] = currentLevel;
      if (comment.responseComments?.length) {
        Object.assign(levels, getCommentNestingLevels(comment.responseComments, currentLevel + 1));
      }
    });
    
    return levels;
  };

  const addComment = async (parentId: number | undefined = undefined, content?: string) => {
    // Determina qual conteúdo usar baseado no parentId
    const commentContent = parentId
      ? replyStates[parentId]?.replyContent
      : content;  // Use the passed content directly

    // Verifica se o conteúdo relevante está vazio
    if (!commentContent?.trim() || !currentVideo || !userId || !user) {
      return;
    }

    // Check nesting level if this is a reply
    if (parentId) {
      const nestingLevel = getCommentNestingLevel(comments, parentId);
      if (nestingLevel >= 2) { // If parent is at level 2, this would be level 3
        alert("Não é possível responder além do terceiro nível de comentários.");
        return;
      }
    }

    // Detecta se é dúvida (apenas para comentários raiz)
    let isDoubt = false;
    if (!parentId && typeof window !== 'undefined') {
      // Busca o valor do checkbox na DOM
      const doubtCheckbox = document.querySelector('input[type="checkbox"][data-doubt-toggle]') as HTMLInputElement;
      isDoubt = doubtCheckbox?.checked || false;
    }

    try {
      const response = await SapsAPI.videoController.addCommentToVideo({
        videoId: currentVideo.id,
        userId: userId,
        username: user.name,
        content: commentContent,
        parentCommentId: parentId,
        isDoubt: !parentId ? isDoubt : undefined
      });

      const newComment: IComment = {
        id: response.id,
        userId: userId,
        username: user.name,
        content: commentContent,
        parentCommentId: parentId,
        likes: [],
        responseComments: [],
        createdAt: new Date(),
        isDoubt: response.isDoubt || false,
      };

      if(!parentId) {
        setLocalComments(prevComments => [newComment, ...prevComments]);
      }

      // Update video statistics
      setInteractionInformation(prev => ({
        ...prev,
        video: {
          ...prev.video,
          numberOfComments: prev.video.numberOfComments + 1
        }
      }));

      // Update comments state
      setComments(prevComments => {
        if (!parentId) {
          // For root-level comments, add to the beginning of the array
          return [newComment, ...prevComments];
        }

        // For nested comments, traverse the tree and update the appropriate parent
        const updateCommentsRecursively = (comments: IComment[]): IComment[] => {
          return comments.map(comment => {
            if (comment.id === parentId) {
              return {
                ...comment,
                responseComments: [...(comment.responseComments || []), newComment]
              };
            }
            if (comment.responseComments?.length) {
              return {
                ...comment,
                responseComments: updateCommentsRecursively(comment.responseComments)
              };
            }
            return comment;
          });
        };

        return updateCommentsRecursively(prevComments);
      });

      // Clear input states
      if (parentId) {
        setReplyStates(prev => ({
          ...prev,
          [parentId]: {
            isReplying: false,
            replyContent: ''
          }
        }));
      } else {
        setNewComment('');
      }
    } catch (error) {
      console.error("Erro ao adicionar comentário:", error);
    }
  };

  // Função para gerenciar a interação de like em um comentário
  const handleClickLikeComment = async (commentId: number) => {
    if (!userId || !user) return;
    
    try {
      const response = await SapsAPI.videoController.addLikeToComment({
        commentId: commentId.toString(),
        userId: userId
      });
      
      // Update the local state to reflect the like
      setComments(prevComments => {
        const updateCommentsRecursively = (comments: IComment[]): IComment[] => {
          return comments.map(comment => {
            if (comment.id === commentId) {
              // Check if the user has already liked the comment
              const hasLiked = comment.likes?.some(like => like.userId === userId);
              
              if (hasLiked) {
                // If already liked, remove the like
                return {
                  ...comment,
                  likes: (comment.likes || []).filter(like => like.userId !== userId)
                };
              } else {
                // If not liked, add the like
                const newLike: ICommentLike = {
                  id: Date.now(), // Temporary ID until we get the real one from the server
                  userId,
                  commentId,
                  comment: comment
                };
                return {
                  ...comment,
                  likes: [...(comment.likes || []), newLike]
                };
              }
            }
            if (comment.responseComments?.length) {
              return {
                ...comment,
                responseComments: updateCommentsRecursively(comment.responseComments)
              };
            }
            return comment;
          });
        };
        
        return updateCommentsRecursively(prevComments);
      });

      // Update user's liked comments in the interaction information
      setInteractionInformation(prev => {
        const likedCommentsIds = [...prev.user.userLikedCommentsIds];
        const commentIdIndex = likedCommentsIds.indexOf(commentId);
        
        if (commentIdIndex === -1) {
          likedCommentsIds.push(commentId);
        } else {
          likedCommentsIds.splice(commentIdIndex, 1);
        }
        
        return {
          ...prev,
          user: {
            ...prev.user,
            userLikedCommentsIds: likedCommentsIds
          }
        };
      });
    } catch (error) {
      console.error("Error liking comment:", error);
    }
  };

  /************************* Handlers de UI: gerencia o estado de expansão da descrição *************************/
  const checkDescriptionLength = (description: string) => {
    const element = document.createElement('div')
    element.style.visibility = 'hidden'
    element.style.width = '100%'
    element.style.position = 'absolute'
    element.style.left = '-9999px'
    element.innerHTML = description
    document.body.appendChild(element)

    const isLong = element.offsetHeight > 48
    setIsDescriptionLong(isLong)

    document.body.removeChild(element)
  }

  useEffect(() => {
    checkDescriptionLength(currentVideo?.description || '')
  }, [currentVideo])

  useEffect(() => {
    setHasMarkedAsWatched(false);
  }, [videoId]); // Reseta quando o vídeo mudar


  /************************* Memoização do Player: para evitar re-renderizações desnecessárias *************************/
  const MemoizedVideoPlayer = useMemo(() => (
    <VideoPlayer
      videoUrl={currentVideo?.videoUrl}
      onWatchEvent={memoizedHandleVideoWatched}
      onReadyEvent={handleVideoLoaded}
      width={playerDimensions.width}
      height={playerDimensions.height}
      containerRef={videoContainerRef}
    />
  ), [currentVideo?.videoUrl, playerDimensions.width, playerDimensions.height, memoizedHandleVideoWatched,]);




  /************************* Renderização condicional: caso o usuário não esteja logado, não renderiza nada *************************/
  if (!user)
    return <></>

  if (!user || !userId)
    return <></>

  if (!currentPlaylist || !currentVideo) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-16 w-16 animate-spin text-blue-400" />
      </div>
    );
  }




  /************************* Renderização final: renderiza o conteúdo principal *************************/

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#040A2F] to-[#0F2057] text-blue-100 py-4 sm:py-6 lg:py-8 px-2 sm:px-4 lg:px-8 relative">
      {/* Estrelas pulsantes */}
      <StarBackground stars={stars} />

      <div className="max-w-8xl mx-auto relative">
        <div className="flex flex-col lg:flex-row gap-4 md:gap-6 lg:gap-8">
          <div className="w-full lg:w-4/5 relative overflow-hidden">
            {/* Conteúdo principal */}
            <div className="relative z-10">
              <div ref={videoContainerRef} className="w-full">
                {MemoizedVideoPlayer}
              </div>
              <VideoInfo
                title={currentVideo.title}
                author={currentVideo.teacher}
                interactionInfo={interactionInformation}
                onLike={handleClickLikeButton}
                onSave={handleClickSaveButton}
              />
              <Attachments videoFiles={currentVideo.videoFiles || []} />
              <Description
                description={currentVideo.description}
                isLong={isDescriptionLong}
                expanded={expandedDescription}
                toggleExpanded={() => setExpandedDescription(!expandedDescription)}
              />
              <CommentsSection
                comments={[...comments]}
                addComment={addComment}
                handleLike={handleClickLikeComment}
                replyStates={replyStates}
                toggleReply={toggleReplyTo}
                updateReplyContent={updateReplyContent}
                avatarImage={avatarImage}
                commentNestingLevels={getCommentNestingLevels(comments)}
                userId={userId as string}
              />
            </div>
          </div>
          <div className="w-full lg:w-1/5 mt-6 lg:mt-0">
            <PlaylistsSidebar
              playlists={playlists.filter(playlist => playlist.olympiads.some(olympiad => olympiad === olympiadId))}
              expandedPlaylists={expandedPlaylists}
              togglePlaylist={togglePlaylist}
              handleVideoChange={handleVideoChange}
              currentPlaylistId={Number(playlistId)}
              currentVideoId={currentVideo.id}
            />
          </div>
        </div>
      </div>
    </div>
  )
}