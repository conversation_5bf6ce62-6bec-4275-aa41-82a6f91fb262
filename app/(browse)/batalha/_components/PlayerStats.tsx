import { motion } from 'framer-motion'
import { Trophy, Users, Star, TrendingUp } from 'lucide-react'
import { estatisticas } from '../_constants/stats'
import XPStar from '@/components/animated_icons/XPStar'
import { PlayerData } from '@/services/GAMES/interfaces/player'

interface PlayerStatsProps {
  playerData: PlayerData | null
}

export default function PlayerStats({ playerData }: PlayerStatsProps) {
  return (
    <motion.div 
      className="bg-blue-900/40 rounded-xl p-6 backdrop-blur-sm"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
    >
      <h3 className="text-2xl font-bold mb-6 flex items-center gap-2">
        <Trophy className="w-6 h-6 text-yellow-400" />
        Suas Estatísticas
      </h3>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        <div className="bg-blue-800/30 p-4 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Users className="w-5 h-5 text-blue-400" />
            <span>Batalhas</span>
          </div>
          <p className="text-2xl font-bold">{playerData?.totalGames}</p>
        </div>
        <div className="bg-blue-800/30 p-4 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Star className="w-5 h-5 text-yellow-400" />
            <span>Vitórias</span>
          </div>
          <p className="text-2xl font-bold text-green-400">{playerData?.wins}</p>
        </div>
        <div className="bg-blue-800/30 p-4 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <TrendingUp className="w-5 h-5 text-purple-400" />
            <span>XP Total Ganho</span>
          </div>
          <XPStar xp={playerData?.score} className="bg-blue-900/40" />
        </div>
      </div>
    </motion.div>
  )
} 