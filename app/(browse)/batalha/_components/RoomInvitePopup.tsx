"use client"
import { motion, AnimatePresence } from 'framer-motion'
import type { InvitedToRoomResponse } from '@/services/GAMES/interfaces/room'
import { useRouter } from 'next/navigation'
import { useChallenge } from '@/context/ChallengeContext'

interface RoomInvitePopupProps {
  invitation: InvitedToRoomResponse;
  onClose: () => void;
}

const RoomInvitePopup = ({ invitation, onClose }: RoomInvitePopupProps) => {
  const router = useRouter()
  const { joinRoom } = useChallenge()

  const handleAccept = async () => {
    try {
      await joinRoom(invitation.room.id)
      onClose()
    } catch (error) {
      console.error('Erro ao aceitar convite:', error)
    }
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 50 }}
        className="fixed bottom-4 right-4 z-50 bg-blue-900/90 backdrop-blur-sm p-4 rounded-lg shadow-lg border border-blue-500"
      >
        <h3 className="text-lg font-semibold mb-2">
          Convite para Batalha!
        </h3>
        <p className="text-sm mb-4">
          {invitation.owner.name} te convidou para uma batalha!
        </p>
        <div className="flex gap-2">
          <button
            onClick={handleAccept}
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 rounded-md text-sm"
          >
            Aceitar
          </button>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-md text-sm"
          >
            Recusar
          </button>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}

export default RoomInvitePopup 