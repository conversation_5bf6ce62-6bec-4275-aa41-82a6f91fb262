import { motion } from 'framer-motion'
import { History } from 'lucide-react'
import { duelosAnteriores } from '../_constants/stats'
import XPStar from '@/components/animated_icons/XPStar'

export default function DueloHistory() {
  return (
    <motion.div 
      className="bg-blue-900/40 rounded-xl p-6"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.4 }}
    >
      <h3 className="text-2xl font-bold mb-6 flex items-center gap-2">
        <History className="w-6 h-6 text-blue-400" />
        Duelos Recentes
      </h3>
      <div className="space-y-4">
        {duelosAnteriores.map((duelo) => (
          <motion.div 
            key={duelo.id}
            className="bg-blue-800/30 p-4 rounded-lg hover:bg-blue-800/40 transition-colors duration-200"
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            <div className="flex justify-between items-center">
              <div>
                <p className="font-semibold">{duelo.oponente}</p>
                <p className="text-sm text-blue-300">{duelo.materia}</p>
              </div>
              <div className="text-right flex flex-col items-end gap-1">
                <XPStar 
                  xp={duelo.pontos} 
                  className={`
                    ${duelo.resultado === 'vitória' 
                      ? 'bg-green-500/20 shadow-lg shadow-green-500/50 border border-green-400/50' 
                      : 'bg-red-500/20 shadow-lg shadow-red-500/50 border border-red-400/50'
                    }
                    animate-pulse
                  `}
                />
                <p className="text-xs text-blue-300">
                  {new Date(duelo.data).toLocaleDateString()}
                </p>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  )
}