import { motion, AnimatePresence } from 'framer-motion'
import { Bell, UserPlus, Clock, Trophy, XCircle, AlertCircle } from 'lucide-react'
import { useChallenge } from '@/context/ChallengeContext'
import { useUserInformation } from '@/context/UserContext'
import { Button } from '@/components/ui/button'
import { useState } from 'react'

export default function BattleInvites() {
  const { roomInvitation, setRoomInvitation, joinRoom } = useChallenge()
  const { user } = useUserInformation()
  const [showErrorPopup, setShowErrorPopup] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const [showXpWarning, setShowXpWarning] = useState(false)
  const [requiredXp, setRequiredXp] = useState(0)

  const handleAcceptInvite = async () => {
    if (roomInvitation) {
      // Verificar XP do usuário

      const userXp = user?.balance?.xp || 0
      const requiredXp = roomInvitation.room.rules?.xpUnit || 0
      
      if (userXp < requiredXp) {
        setRequiredXp(requiredXp)
        setShowXpWarning(true)
        return
      }
      
      try {
        await joinRoom(roomInvitation.room.id)
      } catch (error) {
        console.error('Erro ao aceitar convite:', error)
        
        // Exibir popup de erro
        setErrorMessage('A sala não está mais disponível')
        setShowErrorPopup(true)
        
        // Remover o convite após 3 segundos
        setTimeout(() => {
          setRoomInvitation(null)
          setShowErrorPopup(false)
        }, 3000)
      }
    }
  }
  
  const handleRejectInvite = () => {
    setRoomInvitation(null)
  }

  return (
    <motion.div
      className="lg:col-span-1 bg-blue-900/40 rounded-xl p-6 backdrop-blur-sm relative"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
    >
      {/* Modal de aviso de XP insuficiente */}
      <AnimatePresence>
        {showXpWarning && (
          <motion.div 
            className="fixed inset-0 bg-black/70 flex items-center justify-center z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div 
              className="bg-gradient-to-b from-blue-900 to-purple-900 p-6 rounded-xl max-w-md border border-purple-500/30"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <div className="flex items-center gap-3 mb-4 text-red-300">
                <AlertCircle className="w-7 h-7 flex-shrink-0" />
                <h3 className="text-xl font-bold">XP Insuficiente!</h3>
              </div>
              
              <p className="mb-4 text-blue-100">
                Você precisa de pelo menos <span className="font-bold text-yellow-300">{requiredXp} XP</span> para aceitar este convite de batalha.
              </p>
              
              <p className="mb-6 text-sm text-blue-300">
                Continue explorando para ganhar mais XP e volte para enfrentar este desafio!
              </p>
              
              <div className="flex justify-end">
                <motion.button
                  className="px-4 py-2 bg-blue-700 hover:bg-blue-600 rounded-lg text-blue-100"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setShowXpWarning(false)}
                >
                  Entendi
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Popup de erro */}
      <AnimatePresence>
        {showErrorPopup && (
          <motion.div 
            className="absolute top-0 left-0 right-0 bg-red-500/90 text-white py-3 px-4 rounded-t-xl flex items-center justify-between"
            initial={{ y: -50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -50, opacity: 0 }}
          >
            <div className="flex items-center gap-2">
              <XCircle className="w-5 h-5" />
              <span>{errorMessage}</span>
            </div>
            <button 
              onClick={() => setShowErrorPopup(false)}
              className="text-white/80 hover:text-white"
            >
              &times;
            </button>
          </motion.div>
        )}
      </AnimatePresence>
      
      <h3 className="text-2xl font-bold mb-6 flex items-center gap-2">
        <Bell className="w-6 h-6 text-yellow-400" />
        Convites para Batalha
      </h3>

      {roomInvitation ? (
        <motion.div 
          className="bg-gradient-to-r from-blue-800/50 to-purple-800/50 p-5 rounded-lg border border-blue-500/30"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ type: "spring", stiffness: 200, damping: 15 }}
        >
          <div className="flex flex-col gap-4">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                <UserPlus className="w-6 h-6 text-blue-100" />
              </div>
              <div>
                <h4 className="text-xl font-semibold text-blue-100 break-words">
                  {roomInvitation.owner?.name || "Alguém"} convidou você!
                </h4>
                <p className="text-blue-300 flex items-center gap-1">
                  <Clock className="w-4 h-4 flex-shrink-0" /> Novo desafio!
                </p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 xs:grid-cols-2 gap-2">
              <Button
                onClick={handleAcceptInvite} 
                className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-medium py-2 px-4 rounded-md transition-all"
              >
                Aceitar
              </Button>
              <Button
                variant="outline"
                className="border border-blue-400/30 text-blue-100 hover:bg-blue-700/30"
                onClick={handleRejectInvite}
              >
                Recusar
              </Button>
            </div>
          </div>
          
          <div className="mt-4 bg-blue-950/50 p-3 rounded-md">
            <div className="flex items-center gap-2 text-yellow-300 mb-1">
              <Trophy className="w-4 h-4 flex-shrink-0" />
              <span className="font-medium">Detalhes da Batalha</span>
            </div>
            <p className="text-blue-200 text-sm">
              Junte-se à sala para uma batalha de conhecimentos em {roomInvitation.room.category}!
            </p>
            {roomInvitation.room.rules && (
              <div className="mt-2 text-sm border-t border-blue-700/50 pt-2">
                <p className="text-yellow-400">
                  XP em Jogo: <span className="font-bold">{roomInvitation.room.rules.xpUnit}</span>
                </p>
                <p className="text-blue-300 text-xs">
                  {roomInvitation.room.rules.grade} • {roomInvitation.room.rules.numberOfStages} questões • {roomInvitation.room.rules.lives} vidas
                </p>
              </div>
            )}
          </div>
        </motion.div>
      ) : (
        <div className="text-center py-10 text-blue-300">
          <motion.div 
            className="w-16 h-16 bg-blue-800/40 rounded-full flex items-center justify-center mx-auto mb-4"
            animate={{ 
              scale: [1, 1.1, 1],
              opacity: [0.7, 1, 0.7]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatType: "loop"
            }}
          >
            <Bell className="w-8 h-8 text-blue-300" />
          </motion.div>
          <p className="text-lg">Você não tem convites para batalhas no momento.</p>
          <p className="mt-2 text-sm text-blue-400">Os convites aparecerão aqui quando outro explorador desafiar você!</p>
        </div>
      )}
    </motion.div>
  )
} 