"use client"
import { motion, AnimatePresence } from 'framer-motion'
import { Users, Gamepad2, RefreshCw, AlertCircle, Award, BookOpen } from 'lucide-react'
import { useEffect, useState } from 'react'
import { RoomData } from '@/services/GAMES/interfaces/room'
import { GameCommandService, GameEventService } from '@/services/GAMES'
import { useChallenge } from '@/context/ChallengeContext'
import { useUserInformation } from '@/context/UserContext'

export default function PublicRooms() {
  const { joinRoom } = useChallenge()
  const { user } = useUserInformation()
  const [publicRooms, setPublicRooms] = useState<RoomData[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showXpWarning, setShowXpWarning] = useState(false)
  const [requiredXp, setRequiredXp] = useState(0)

  const commandService = new GameCommandService()
  const eventService = new GameEventService()

  useEffect(() => {
    // Escuta a lista de salas públicas
    eventService.onPublicRoomsList((data) => {
      setPublicRooms(data.rooms)

      setIsLoading(false)
    })

    // Busca inicial de salas
    fetchPublicRooms()


    return () => {
      // Limpar os listeners se necessário
    }
  }, [])

  const fetchPublicRooms = async () => {
    setIsLoading(true)
    try {
      await commandService.getPublicRooms()
      // O evento onPublicRoomsList vai atualizar a lista e desativar o loading
    } catch (error) {
      console.error("Erro ao buscar salas públicas:", error)
      setIsLoading(false)
    }
  }

  const handleJoinRoom = (roomId: string, requiredXp: number) => {
    // Verificar se o jogador tem XP suficiente

    const userXp = user?.balance?.xp || 0
    
    if (userXp < requiredXp) {
      setRequiredXp(requiredXp)
      setShowXpWarning(true)
      return
    }
    
    joinRoom(roomId)
  }

  return (
    <motion.div 
      className="bg-blue-900/40 rounded-xl p-6 col-span-full relative"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.6 }}
    >
      {/* Modal de aviso de XP insuficiente */}
      <AnimatePresence>
        {showXpWarning && (
          <motion.div 
            className="fixed inset-0 bg-black/70 flex items-center justify-center z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div 
              className="bg-gradient-to-b from-blue-900 to-purple-900 p-6 rounded-xl max-w-md border border-purple-500/30"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <div className="flex items-center gap-3 mb-4 text-red-300">
                <AlertCircle className="w-7 h-7 flex-shrink-0" />
                <h3 className="text-xl font-bold">XP Insuficiente!</h3>
              </div>
              
              <p className="mb-4 text-blue-100">
                Você precisa de pelo menos <span className="font-bold text-yellow-300">{requiredXp} XP</span> para entrar nesta sala de batalha.
              </p>
              
              <p className="mb-6 text-sm text-blue-300">
                Continue explorando para ganhar mais XP e volte para enfrentar este desafio!
              </p>
              
              <div className="flex justify-end">
                <motion.button
                  className="px-4 py-2 bg-blue-700 hover:bg-blue-600 rounded-lg text-blue-100"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setShowXpWarning(false)}
                >
                  Entendi
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-2xl font-bold flex items-center gap-2">
          <Gamepad2 className="w-6 h-6 text-green-400" />
          Salas Públicas Disponíveis
        </h3>
        
        <motion.button
          className={`p-2 rounded-full bg-blue-700/40 border border-blue-600/30 flex-shrink-0
            ${isLoading ? 'opacity-70 cursor-wait' : 'hover:bg-blue-700/60'}`}
          whileTap={{ scale: 0.95 }}
          onClick={fetchPublicRooms}
          disabled={isLoading}
          title="Atualizar salas disponíveis"
        >
          <RefreshCw 
            className={`w-5 h-5 text-blue-300 ${isLoading ? 'animate-spin' : ''}`} 
          />
        </motion.button>
      </div>

      {publicRooms.length === 0 ? (
        <div className="text-center py-8 text-blue-300">
          {isLoading ? (
            <div className="flex flex-col items-center">
              <RefreshCw className="w-8 h-8 text-blue-400 animate-spin mb-3" />
              <p>Buscando salas disponíveis...</p>
            </div>
          ) : (
            <p>Nenhuma sala pública disponível no momento.</p>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {publicRooms.map((room) => (
            <motion.div
              key={room.id}
              className="bg-blue-800/30 p-4 rounded-lg hover:bg-blue-800/40 transition-colors duration-200 cursor-pointer"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h4 className="font-semibold text-lg capitalize">{room.category}</h4>
                  <p className="text-sm text-blue-300">
                    Criado por: {room.players.find(p => p.userId === room.ownerId)?.name || " "}
                  </p>
                </div>
                <span className="bg-blue-700/50 px-3 py-1 rounded-full text-xs">
                  <Users className="w-4 h-4 inline-block mr-1" />
                  {room.currentPlayers}/{room.maxCapacity}
                </span>
              </div>
              
              {/* Informações extras da sala */}
              <div className="mb-3 grid grid-cols-2 gap-2 text-xs">
                <div className="bg-blue-950/50 p-2 rounded-md flex items-center gap-1">
                  <Award className="w-3.5 h-3.5 text-yellow-400" />
                  <span className="text-yellow-300 font-medium">{room.rules?.xpUnit || 0} XP apostado</span>
                </div>
                <div className="bg-blue-950/50 p-2 rounded-md flex items-center gap-1">
                  <BookOpen className="w-3.5 h-3.5 text-green-400" />
                  <span className="text-green-300">{room.rules?.grade}</span>
                </div>
              </div>

              <div className="flex justify-between items-center">
                <span className={`text-sm px-2 py-1 rounded-full ${
                  room.isInPlay 
                    ? 'bg-yellow-500/20 text-yellow-300'
                    : 'bg-green-500/20 text-green-300'
                }`}>
                  {room.isInPlay ? 'Em Jogo' : 'Aguardando'}
                </span>
                <motion.button
                  className="px-4 py-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full text-sm font-bold hover:from-blue-500 hover:to-purple-500 transition-all shadow-lg shadow-purple-500/20 disabled:opacity-50 disabled:cursor-not-allowed"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleJoinRoom(room.id, room.rules?.xpUnit || 0)}
                  disabled={room.isInPlay || room.currentPlayers >= room.maxCapacity}
                >
                  {room.currentPlayers >= room.maxCapacity ? 'Sala Cheia' : 'Entrar'}
                </motion.button>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </motion.div>
  )
} 