import { motion, AnimatePresence } from 'framer-motion'
import { Swords, AlertCircle } from 'lucide-react'
import Link from 'next/link'
import XPStar from '@/components/animated_icons/XPStar'
import { useState } from 'react'
import { apostasDisponiveis } from '../_constants/stats'
import { useUserInformation } from '@/context/UserContext'

export default function DueloCard() {
  const { user } = useUserInformation()
  const [xpSelecionado, setXpSelecionado] = useState<number>(apostasDisponiveis[0])
  const [showXpWarning, setShowXpWarning] = useState(false)
  
  const handleCreateBattle = (e: React.MouseEvent) => {
    const userXp = user?.balance?.xp || 0
    
    if (userXp < xpSelecionado) {
      e.preventDefault()
      setShowXpWarning(true)
      return
    }
  }

  return (
    <>
      {/* Modal de aviso de XP insuficiente */}
      <AnimatePresence>
        {showXpWarning && (
          <motion.div 
            className="fixed inset-0 bg-black/70 flex items-center justify-center z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div 
              className="bg-gradient-to-b from-blue-900 to-purple-900 p-6 rounded-xl max-w-md border border-purple-500/30"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <div className="flex items-center gap-3 mb-4 text-red-300">
                <AlertCircle className="w-7 h-7 flex-shrink-0" />
                <h3 className="text-xl font-bold">XP Insuficiente!</h3>
              </div>
              
              <p className="mb-4 text-blue-100">
                Você precisa de pelo menos <span className="font-bold text-yellow-300">{xpSelecionado} XP</span> para criar esta sala de batalha.
              </p>
              
              <p className="mb-6 text-sm text-blue-300">
                Continue explorando para ganhar mais XP e volte para criar este desafio!
              </p>
              
              <div className="flex justify-end">
                <motion.button
                  className="px-4 py-2 bg-blue-700 hover:bg-blue-600 rounded-lg text-blue-100"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setShowXpWarning(false)}
                >
                  Entendi
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      <Link href={`/batalha/sala-de-selecao?xp=${xpSelecionado}`} onClick={handleCreateBattle}>
        <motion.div 
          className="bg-blue-900/40 rounded-xl p-8 text-center hover:bg-blue-800/40 transition-colors cursor-pointer border border-blue-500/30"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            className="w-24 h-24 mx-auto mb-6 relative"
          >
            <Swords className="w-full h-full text-blue-400" />
          </motion.div>
          <h2 className="text-3xl font-bold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">
            Criar Nova Batalha
          </h2>
          <p className="text-blue-300 mb-6">
            Desafie seus amigos para uma batalha épica de conhecimento!
          </p>

          <div className="mb-6">
            <p className="text-sm text-blue-300 mb-3">Selecione quanto XP deseja por em jogo:</p>
            <div className="flex flex-wrap justify-center gap-3">
              {apostasDisponiveis.map((xp) => (
                <motion.button
                  key={xp}
                  onClick={(e) => {
                    e.preventDefault()
                    setXpSelecionado(xp)
                  }}
                  className={`relative ${
                    xpSelecionado === xp 
                      ? 'ring-2 ring-purple-500 ring-offset-2 ring-offset-blue-900' 
                      : 'hover:scale-105'
                  } transition-all rounded-lg`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <XPStar xp={xp} />
                </motion.button>
              ))}
            </div>
          </div>

          <motion.button 
            className="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full font-bold text-lg hover:from-blue-500 hover:to-purple-500 transition-all shadow-lg shadow-purple-500/20"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Começar Batalha por <span className="text-yellow-300">{xpSelecionado} XP</span>
          </motion.button>
        </motion.div>
      </Link>
    </>
  )
} 