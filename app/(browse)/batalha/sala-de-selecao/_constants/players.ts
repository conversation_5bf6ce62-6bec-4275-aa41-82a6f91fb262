export type Player = {
  id: string
  name: string
  score: number
  wins: number
  totalGames: number
  avatar: string
  xp?: number
  online: boolean
  userId: string
}

export const jogadoresOnline: Player[] = [
  {
    id: "1",
    name: "<PERSON>",
    avatar: "https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=random",
    score: 2500,
    wins: 100,
    totalGames: 100,
    online: true,
    userId: "1"
  },
  {
    id: "2",
    name: "<PERSON>",
    avatar: "https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=random",
    score: 1800,
    wins: 100,
    totalGames: 100,
    online: true,
    userId: "2"
  },
  {
    id: "3",
    name: "<PERSON>",
    avatar: "https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=random",
    score: 3200,
    wins: 100,
    totalGames: 100,
    online: false,
    userId: "3"
  },
  {
    id: "4",
    name: "<PERSON>",
    avatar: "https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=random",
    score: 2100,
    wins: 100,
    totalGames: 100,
    online: true,
    userId: "4"
  }
] as const 