import { Search, Users } from 'lucide-react'

type SearchBarProps = {
  searchTerm: string
  onSearchChange: (value: string) => void
  onlineCount: number
}

export default function SearchBar({ searchTerm, onSearchChange, onlineCount }: SearchBarProps) {
  return (
    <div className="flex items-center gap-4 mb-6">
      <div className="flex-1 relative">
        <input
          type="text"
          placeholder="Buscar jogadores..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="w-full bg-blue-800/30 border border-blue-700/50 rounded-lg px-4 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-purple-500"
        />
        <Search className="absolute left-3 top-2.5 h-5 w-5 text-blue-400" />
      </div>
      <div className="flex items-center gap-2 text-blue-300">
        <Users className="h-5 w-5" />
        <span>{onlineCount} exploradores online</span>
      </div>
    </div>
  )
} 