import { motion } from 'framer-motion'
import Image from 'next/image'
import XPStar from '@/components/animated_icons/XPStar'
import { CheckCircle } from 'lucide-react'
import type { Player } from '../_constants/players'

type PlayerCardProps = {
  player: Player
  isSelected: boolean
  onSelect: (id: string) => void
  xpApostado: number
}

export default function PlayerCard({ player, isSelected, onSelect, xpApostado }: PlayerCardProps) {
    
  return (
    <motion.button
      onClick={() => onSelect(player.id)}
      className={`w-full p-4 rounded-lg transition-all ${
        isSelected
          ? 'bg-purple-500/30 border-2 border-purple-400'
          : 'bg-blue-800/30 hover:bg-blue-800/50'
      }`}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="relative">
            <Image
              src={player.avatar}
              alt={player.name}
              width={48}
              height={48}
              className="rounded-full"
            />
            <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-blue-900 ${
              player.online ? 'bg-green-500' : 'bg-gray-500'
            }`} />
          </div>
          <div className="text-left">
            <h3 className="font-bold">{player.name}</h3>
            <div className="flex items-center gap-2">
              <XPStar xp={player.score || 0} />
              {isSelected && (
                <>
                  <span className="text-blue-300">•</span>
                  <XPStar 
                    xp={xpApostado} 
                    className="bg-purple-500/20 shadow-lg shadow-purple-500/50"
                  />
                  <span className="text-sm text-purple-300">em jogo</span>
                </>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex items-center">
          {isSelected ? (
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="flex items-center gap-2 text-purple-300"
            >
              <CheckCircle className="w-6 h-6 text-purple-400" />
              <span className="font-medium hidden sm:inline">Selecionado</span>
            </motion.div>
          ) : (
            <motion.div 
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-blue-400 text-sm px-3 py-1 rounded-full border border-blue-500/30 hover:bg-blue-700/30"
            >
              Selecionar
            </motion.div>
          )}
        </div>
      </div>
    </motion.button>
  )
} 