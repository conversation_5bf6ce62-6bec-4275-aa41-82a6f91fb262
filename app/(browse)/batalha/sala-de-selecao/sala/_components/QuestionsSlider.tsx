"use client"
import { motion } from 'framer-motion'
import { Heart } from 'lucide-react'

interface QuestionsSliderProps {
  numQuestoes: number
  setNumQuestoes: (num: number) => void
}

export function QuestionsSlider({ numQuestoes, setNumQuestoes }: QuestionsSliderProps) {
  const questionsOptions = [5, 6, 7]

  return (
    <div className="mb-16 bg-blue-950/30 p-8">
      <div className="max-w-3xl mx-auto">
        <h2 className="text-xl font-semibold mb-12 text-center text-blue-100">
          Selecione o número de questões
        </h2>
        
        <div className="flex flex-col">
          {/* Container dos números e corações */}
          <div className="flex justify-between mb-8">
            {questionsOptions.map((num) => {
              const isSelected = numQuestoes === num
              const numVidas = Math.ceil(num / 3)
              
              return (
                <motion.div 
                  key={num} 
                  className="flex flex-col items-center"
                  initial={false}
                  animate={isSelected ? { y: -4 } : { y: 0 }}
                  transition={{ type: "spring", stiffness: 300, damping: 25 }}
                >
                  {/* Botão com número */}
                  <motion.button
                    onClick={() => setNumQuestoes(num)}
                    className={`w-14 h-14 rounded-full flex items-center justify-center text-2xl font-bold
                      ${isSelected 
                        ? 'bg-gradient-to-r from-indigo-600 to-purple-600 shadow-lg shadow-purple-500/20' 
                        : 'bg-blue-950/30 hover:bg-blue-900/40'
                      } transition-all duration-300`}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    animate={isSelected ? {
                      boxShadow: [
                        "0 0 0 0 rgba(139, 92, 246, 0)",
                        "0 0 0 10px rgba(139, 92, 246, 0.1)",
                        "0 0 0 20px rgba(139, 92, 246, 0)"
                      ]
                    } : {}}
                    transition={isSelected ? {
                      repeat: Infinity,
                      duration: 2
                    } : {}}
                  >
                    {num}
                  </motion.button>

                  {/* Container dos corações */}
                  <motion.div 
                    className="flex flex-col items-center gap-2 mt-4"
                    animate={isSelected ? { scale: 1.1 } : { scale: 1 }}
                  >
                    {Array.from({ length: numVidas }).map((_, index) => (
                      <motion.div
                        key={index}
                        initial={{ scale: 0, y: -10 }}
                        animate={{ scale: 1, y: 0 }}
                        transition={{ 
                          delay: index * 0.1,
                          type: "spring",
                          stiffness: 400,
                          damping: 10
                        }}
                      >
                        <Heart
                          className={`w-6 h-6 transition-all duration-300 ${
                            isSelected 
                              ? 'text-red-500 drop-shadow-glow animate-pulse-subtle' 
                              : 'text-red-500/30'
                          }`}
                          fill={isSelected ? 'currentColor' : 'none'}
                        />
                      </motion.div>
                    ))}
                  </motion.div>
                </motion.div>
              )
            })}
          </div>

          {/* Barra de progresso */}
          <div className="relative h-2">
            {/* Barra de progresso base */}
            <div className="h-2 bg-blue-950/30 rounded-full" />
            
            {/* Barra de progresso ativa */}
            <motion.div 
              className="absolute top-0 left-0 h-2 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full"
              style={{
                width: `${((questionsOptions.indexOf(numQuestoes)) / (questionsOptions.length - 1)) * 100}%`
              }}
              transition={{ 
                type: "spring",
                stiffness: 400,
                damping: 25
              }}
              layoutId="activeBar"
            />
          </div>
        </div>
      </div>
    </div>
  )
} 