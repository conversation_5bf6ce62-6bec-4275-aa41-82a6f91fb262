"use client"
import { motion } from 'framer-motion'
import XPStar from '@/components/animated_icons/XPStar'

interface HeaderProps {
  xpApostado: number
  playersCount: number
}

export function Header({ xpApostado, playersCount }: HeaderProps) {
  return (
    <motion.header 
      className="flex justify-between items-center mb-8"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <div>
        <h1 className="text-4xl font-bold mb-3 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">
          Configurar Duelo
        </h1>
        <p className="text-lg text-indigo-200">
          Duelo com{' '}
          <span className="font-bold text-purple-300">
            {playersCount} {playersCount === 1 ? 'oponente' : 'oponentes'}
          </span>{' '}
          e{' '}
          <motion.span 
            className="font-bold text-yellow-300"
            whileHover={{ scale: 1.1 }}
          >
            {xpApostado} XP
          </motion.span>{' '}
          em jogo!
        </p>
      </div>
      <motion.div 
        className="flex flex-col items-end gap-2"
        whileHover={{ scale: 1.05 }}
      >
        <XPStar xp={xpApostado} className="bg-purple-500/20 shadow-lg shadow-purple-500/50" />
        <p className="text-sm text-indigo-200">XP em jogo</p>
      </motion.div>
    </motion.header>
  )
} 