"use client"
import { useState } from 'react'
import { motion } from 'framer-motion'
import { Book, GraduationCap } from 'lucide-react'

const subjects = [
  { id: 'matematica', name: '<PERSON><PERSON><PERSON><PERSON>' },
  { id: 'portugues', name: 'Port<PERSON><PERSON><PERSON><PERSON>' },
  { id: 'cien<PERSON><PERSON>', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { id: 'historia', name: '<PERSON><PERSON><PERSON><PERSON>' },
  { id: 'geografia', name: 'Geografia' }
]

export const levels = [
  { id: 6, name: '6° Ano' },
  { id: 7, name: '7° Ano' },
  { id: 8, name: '8° Ano' },
  { id: 9, name: '9° Ano' }
]

interface SubjectAndLevelSelectorProps {
  selectedSubject: string
  setSelectedSubject: (subject: string) => void
  selectedLevel: number
  setSelectedLevel: (level: number) => void
}

export function SubjectAndLevelSelector({
  selectedSubject,
  setSelectedSubject,
  selectedLevel,
  setSelectedLevel
}: SubjectAndLevelSelectorProps) {
  return (
    <div className="space-y-8 my-12">
      <div>
        <h3 className="flex items-center gap-2 text-xl font-semibold mb-4">
          <Book className="w-5 h-5" />
          Selecione a Matéria
        </h3>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-3">
          {subjects.map((subject) => (
            <motion.button
              key={subject.id}
              className={`p-3 rounded-lg text-center transition-all ${
                selectedSubject === subject.id
                  ? 'bg-blue-600 text-white shadow-lg shadow-blue-500/50'
                  : 'bg-blue-950/50 hover:bg-blue-900/50 hover:shadow-md hover:shadow-blue-500/30'
              }`}
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              onClick={() => setSelectedSubject(subject.id)}
            >
              {subject.name}
            </motion.button>
          ))}
        </div>
      </div>

      <div>
        <h3 className="flex items-center gap-2 text-xl font-semibold mb-4">
          <GraduationCap className="w-5 h-5" />
          Selecione o Nível
        </h3>
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
          {levels.map((level) => (
            <motion.button
              key={level.id}
              className={`p-3 rounded-lg text-center transition-all ${
                selectedLevel === level.id
                  ? 'bg-blue-600 text-white shadow-lg shadow-blue-500/50'
                  : 'bg-blue-950/50 hover:bg-blue-900/50 hover:shadow-md hover:shadow-blue-500/30'
              }`}
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              onClick={() => setSelectedLevel(level.id)}
            >
              {level.name}
            </motion.button>
          ))}
        </div>
      </div>
    </div>
  )
} 