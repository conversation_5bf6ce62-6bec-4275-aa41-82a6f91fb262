import { TooltipProvider } from '@/components/ui/tooltip'
import React, { ReactNode } from 'react'
import 'katex/dist/katex.min.css'

const Layout = ({children}: {children: ReactNode}) => {
  return (
    <div className="h-[calc(100vh-64px)] flex flex-col bg-gradient-to-b from-[#040A2F] to-[#0F2057]">
      <TooltipProvider>
        {children}
      </TooltipProvider>
    </div>
  )
}

export default Layout