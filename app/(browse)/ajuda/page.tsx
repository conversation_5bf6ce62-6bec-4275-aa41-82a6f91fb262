import dynamic from 'next/dynamic'

// Use dynamic import with no SSR to prevent document not defined errors
const ChatComponent = dynamic(() => import('./components/ChatComponent'), {
  ssr: false,
  loading: () => (
    <div className="h-screen flex items-center justify-center bg-gradient-to-b from-[#040A2F] to-[#0F2057] text-blue-100">
      <div className="text-xl">Carregando chat espacial...</div>
    </div>
  )
})

export default function AjudaPage() {
  return <ChatComponent />
}


