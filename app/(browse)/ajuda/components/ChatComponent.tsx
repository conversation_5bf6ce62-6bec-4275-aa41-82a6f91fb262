"use client"
import React, { useState, useEffect } from 'react'
import { Send, Loader2 } from 'lucide-react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import dynamic from 'next/dynamic'
import 'katex/dist/katex.min.css'
import <PERSON><PERSON> from 'lottie-react'
import astronautAnimation from '@/assets/animations/astronaut_animation.json' 
import { motion } from 'framer-motion'

// Dynamically import ReactMarkdown to ensure it only loads on client side
// @ts-ignore - Type mismatch but works at runtime
const ReactMarkdown = dynamic(() => import('react-markdown').then(mod => mod.default), { ssr: false })

const agents = [
  { 
    id: 'math', 
    name: 'Professor <PERSON>',
    description: 'Olá! Sou apaixonado por números e vou te ajudar a desvendar os mistérios da matemática de forma divertida! 📐',
    emoji: '🔢'
  },
  { 
    id: 'physics', 
    name: '<PERSON><PERSON><PERSON>',
    description: 'Vamos explorar juntos os fenômenos do universo e entender como tudo funciona! A física é fascinante! ⚡',
    emoji: '⚛️'
  },
  { 
    id: 'chemistry', 
    name: 'Mestre Lavoisier',
    description: 'Pronto para fazer experiências incríveis e descobrir as transformações da matéria? A química é pura magia! 🧪',
    emoji: '🧪'
  },
  { 
    id: 'portuguese', 
    name: 'Professora Clarice',
    description: 'Vamos mergulhar no mundo das palavras e aprender português de forma criativa e envolvente! 📚',
    emoji: '✍️'
  },
  { 
    id: 'history', 
    name: 'Doutor Tempo',
    description: 'Prepare-se para uma viagem fascinante através dos séculos! A história está cheia de aventuras incríveis! 🏰',
    emoji: '📜'
  },
  { 
    id: 'geography', 
    name: 'Exploradora Aurora',
    description: 'Vamos descobrir juntos as maravilhas do nosso planeta, seus lugares, povos e culturas! 🌎',
    emoji: '🗺️'
  },
  { 
    id: 'biology', 
    name: 'Doutora Darwin',
    description: 'Pronta para explorar os mistérios da vida? Vamos aprender sobre os seres vivos de um jeito super divertido! 🧬',
    emoji: '🔬'
  }
]

interface Message {
  id: number;
  text: string;
  sender: 'user' | 'ai';
}

// Skip SSR for math plugins too
const MathMarkdown = ({ children }: { children: string }) => {
  // Load plugins only on client side
  const [mathPlugins, setMathPlugins] = useState<any[]>([])
  
  useEffect(() => {
    const loadPlugins = async () => {
      const [remarkMathModule, rehypeKatexModule] = await Promise.all([
        import('remark-math'),
        import('rehype-katex')
      ])
      setMathPlugins([remarkMathModule.default, rehypeKatexModule.default])
    }
    
    loadPlugins()
  }, [])
  
  if (mathPlugins.length === 0) {
    return <div className="text-blue-100">{children}</div>
  }
  
  return (
    <ReactMarkdown
      remarkPlugins={[mathPlugins[0]]}
      rehypePlugins={[mathPlugins[1]]}
      className="text-sm sm:text-base prose prose-invert max-w-none"
      components={{
        p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
        code: ({ inline, className, children, ...props }) => {
          if (inline) {
            return <code className="bg-blue-900/30 px-1 rounded" {...props}>{children}</code>
          }
          return (
            <pre className="bg-blue-900/30 p-2 rounded-lg overflow-x-auto">
              <code {...props}>{children}</code>
            </pre>
          )
        }
      }}
    >
      {children}
    </ReactMarkdown>
  )
}

// Função mock para simular respostas da IA
const getAIResponse = async (message: string, agentId: string): Promise<string> => {
  try {
    const response = await fetch('/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message,
        agentId
      }),
    })

    if (!response.ok) {
      throw new Error('Falha na comunicação')
    }

    const data = await response.json()
    
    if (data.error) {
      throw new Error(data.error)
    }

    return data.response
  } catch (error) {
    console.error('Erro:', error)
    return 'Desculpe, tive um problema ao processar sua mensagem. Pode tentar novamente?'
  }
}

export default function ChatComponent() {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputMessage, setInputMessage] = useState('')
  const [selectedAgent, setSelectedAgent] = useState(agents[0])
  const [isLoading, setIsLoading] = useState(false)
  const [showWelcome, setShowWelcome] = useState(true)

  const handleSendMessage = async () => {
    if (inputMessage.trim() === '') return

    const newMessage: Message = {
      id: Date.now(),
      text: inputMessage,
      sender: 'user',
    }

    setMessages([...messages, newMessage])
    setInputMessage('')
    setIsLoading(true)

    try {
      const aiResponse = await getAIResponse(inputMessage, selectedAgent.id)
      const aiMessage: Message = {
        id: Date.now() + 1,
        text: aiResponse,
        sender: 'ai',
      };
      setMessages(prevMessages => [...prevMessages, aiMessage]);
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      // Criar estrelas aleatórias com mais densidade e variação
      const starContainer = document.getElementById('star-container')
      if (starContainer) {
        for (let i = 0; i < 300; i++) {
          const star = document.createElement('div')
          star.className = 'absolute bg-white rounded-full animate-twinkle'
          const size = Math.random() * 3 + 1
          star.style.width = `${size}px`
          star.style.height = `${size}px`
          star.style.top = `${Math.random() * 100}%`
          star.style.left = `${Math.random() * 100}%`
          star.style.animationDuration = `${Math.random() * 5 + 2}s`
          star.style.animationDelay = `${Math.random() * 5}s`
          // Adicionar algumas estrelas com brilho
          if (Math.random() > 0.7) {
            star.style.boxShadow = `0 0 ${Math.random() * 5 + 2}px rgba(255, 255, 255, 0.8)`
          }
          starContainer.appendChild(star)
        }
      }
    }
  }, [])

  return (
    <div className="h-full flex items-center justify-center py-2 px-2 sm:py-4 sm:px-4 lg:px-8 relative">
      <div id="star-container" className="absolute inset-0 overflow-hidden pointer-events-none"></div>
      
      {/* Adicionar nebulosas coloridas para efeito futurista */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 -left-20 w-80 h-80 bg-purple-500 opacity-10 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-1/3 -right-20 w-96 h-96 bg-blue-500 opacity-10 rounded-full filter blur-3xl"></div>
        <div className="absolute top-2/3 left-1/3 w-64 h-64 bg-cyan-500 opacity-10 rounded-full filter blur-3xl"></div>
      </div>
      
      {showWelcome ? (
        <div className="h-full max-w-6xl w-full mx-auto bg-blue-900/10 rounded-2xl shadow-lg overflow-hidden flex flex-col backdrop-blur-md z-10 border border-blue-500/30 relative">
          {/* Efeito de borda brilhante */}
          <div className="absolute inset-0 rounded-2xl border border-blue-400/20 shadow-[0_0_15px_rgba(59,130,246,0.3)] pointer-events-none"></div>
          
          <div className="flex-1 p-4 sm:p-6 flex flex-col lg:flex-row lg:items-stretch overflow-hidden">
            {/* Coluna de boas-vindas - em telas grandes fica à esquerda */}
            <div className="lg:w-1/3 flex flex-col justify-center items-center lg:items-start lg:pr-6 lg:border-r lg:border-blue-500/20">
              <motion.div 
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6 }}
                className="w-28 h-28 sm:w-32 sm:h-32 flex-shrink-0 lg:mb-6"
              >
                <Lottie animationData={astronautAnimation} loop={true} />
              </motion.div>
              
              <motion.h1 
                initial={{ y: -10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-2xl sm:text-3xl lg:text-4xl font-bold text-blue-100 text-center lg:text-left flex-shrink-0 mb-4"
              >
                Bem-vindo à Estação Espacial do Conhecimento! 🚀
              </motion.h1>
              
              <motion.p 
                initial={{ y: -10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="text-lg text-blue-200 text-center lg:text-left mb-6 lg:mb-8"
              >
                Escolha seu especialista espacial e embarque em uma jornada incrível de aprendizado!
              </motion.p>
              
              {/* Elemento decorativo apenas visível em telas grandes */}
              <div className="hidden lg:block mt-auto">
                <div className="bg-blue-800/20 p-4 rounded-lg border border-blue-500/20 backdrop-blur-md">
                  <h3 className="text-blue-100 font-medium mb-2">Dica espacial:</h3>
                  <p className="text-blue-200 text-sm">Cada especialista tem conhecimentos únicos. Escolha o que melhor se adapta à sua missão de aprendizado!</p>
                </div>
              </div>
            </div>
            
            {/* Coluna do grid de agentes - em telas grandes fica à direita */}
            <div className="w-full lg:w-2/3 overflow-y-auto custom-scrollbar flex-grow lg:pl-6 mt-6 lg:mt-0">
              <motion.h2
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="text-xl font-bold text-blue-100 mb-4 hidden lg:block"
              >
                Especialistas Disponíveis:
              </motion.h2>
              
              <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-3 p-2 sm:gap-4"
              >
                {agents.map((agent, index) => (
                  <motion.button
                    key={agent.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.1 * index }}
                    onClick={() => {
                      setSelectedAgent(agent)
                      setShowWelcome(false)
                    }}
                    className="bg-blue-800/30 p-4 rounded-xl hover:bg-blue-700/40 transition-all duration-300 border border-blue-500/30 backdrop-blur-md group hover:scale-105 hover:shadow-[0_0_15px_rgba(59,130,246,0.4)]"
                  >
                    <div className="flex flex-col items-center text-center">
                      <div className="text-4xl mb-3 transform group-hover:scale-110 transition-transform duration-300">{agent.emoji}</div>
                      <h3 className="text-lg font-bold text-blue-100 mb-2">{agent.name}</h3>
                      <p className="text-blue-200 text-sm mb-3">{agent.description}</p>
                      <div className="mt-2 text-blue-300 text-sm group-hover:text-blue-100 flex items-center">
                        <span>Iniciar conversa</span>
                        <span className="ml-1 group-hover:ml-2 transition-all duration-300">→</span>
                      </div>
                    </div>
                  </motion.button>
                ))}
              </motion.div>
            </div>
          </div>
        </div>
      ) : (
        <div className="h-full max-w-6xl w-full mx-auto bg-blue-900/10 rounded-2xl shadow-lg overflow-hidden flex flex-col backdrop-blur-md z-10 border border-blue-500/30 relative">
          {/* Efeito de borda brilhante */}
          <div className="absolute inset-0 rounded-2xl border border-blue-400/20 shadow-[0_0_15px_rgba(59,130,246,0.3)] pointer-events-none"></div>
          
          <div className="bg-blue-800/20 px-3 sm:px-6 py-3 flex justify-between items-center backdrop-blur-lg border-b border-blue-500/30">
            <div className="flex items-center space-x-2 sm:space-x-4">
              <button
                onClick={() => setShowWelcome(true)}
                className="text-blue-100 hover:text-blue-200 transition-colors flex items-center group"
              >
                <span className="transform group-hover:-translate-x-1 transition-transform duration-300">←</span>
                <span className="ml-1">Voltar</span>
              </button>
              <div className="flex items-center">
                <span className="text-xl sm:text-2xl mr-2 animate-pulse">{selectedAgent.emoji}</span>
                <div>
                  <h2 className="text-lg sm:text-xl font-bold text-blue-100">{selectedAgent.name}</h2>
                  <p className="text-xs sm:text-sm text-blue-200">Seu guia espacial</p>
                </div>
              </div>
            </div>
            
            <div className="hidden sm:flex">
              <Select
                value={selectedAgent.id}
                onValueChange={(value) => {
                  const newAgent = agents.find(a => a.id === value);
                  if (newAgent) setSelectedAgent(newAgent);
                }}
              >
                <SelectTrigger className="w-[180px] bg-blue-800/30 border-blue-500/30 text-blue-100">
                  <SelectValue placeholder="Selecione um agente" />
                </SelectTrigger>
                <SelectContent className="bg-blue-900/90 border-blue-500/30 text-blue-100">
                  {agents.map((agent) => (
                    <SelectItem key={agent.id} value={agent.id} className="focus:bg-blue-800/50">
                      <div className="flex items-center">
                        <span className="mr-2">{agent.emoji}</span>
                        <span>{agent.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto p-3 sm:p-6 space-y-4 custom-scrollbar">
            <motion.div 
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="bg-blue-800/20 p-3 sm:p-4 rounded-lg backdrop-blur-md mb-4 sm:mb-6 border border-blue-500/20"
            >
              <p className="text-blue-100 text-sm sm:text-base">{selectedAgent.description}</p>
              <p className="text-blue-200 mt-2 text-sm sm:text-base">Como posso te ajudar hoje?</p>
            </motion.div>
            
            {messages.map((message, index) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[85%] sm:max-w-[80%] p-3 sm:p-4 rounded-2xl ${
                    message.sender === 'user'
                      ? 'bg-blue-600/50 text-white rounded-tr-sm border border-blue-400/30'
                      : 'bg-blue-800/50 text-blue-100 rounded-tl-sm border border-blue-500/30'
                  }`}
                >
                  {message.sender === 'ai' && (
                    <div className="flex items-center mb-2">
                      <span className="text-lg mr-2">{selectedAgent.emoji}</span>
                      <span className="text-sm font-medium text-blue-200">{selectedAgent.name}</span>
                    </div>
                  )}
                  <MathMarkdown>
                    {message.text}
                  </MathMarkdown>
                </div>
              </motion.div>
            ))}

            {/* Indicador de digitação */}
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-blue-800/50 text-blue-100 rounded-2xl rounded-tl-sm p-3 flex items-center space-x-2 border border-blue-500/30">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm">{selectedAgent.name} está digitando...</span>
                </div>
              </div>
            )}
          </div>

          <div className="bg-blue-800/20 p-2 sm:p-4 backdrop-blur-lg border-t border-blue-500/30">
            <div className="flex space-x-2 max-w-4xl mx-auto">
              <input
                type="text"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                placeholder={`Pergunte algo para ${selectedAgent.name}...`}
                className="flex-grow bg-blue-700/50 text-sm sm:text-base text-blue-100 placeholder-blue-300 rounded-full py-2 sm:py-3 px-4 sm:px-6 focus:outline-none focus:ring-2 focus:ring-blue-400 backdrop-blur-lg border border-blue-500/30"
              />
              <button
                onClick={handleSendMessage}
                disabled={inputMessage.trim() === '' || isLoading}
                className={`${
                  inputMessage.trim() === '' || isLoading 
                    ? 'bg-blue-700/50' 
                    : 'bg-blue-600 hover:bg-blue-500 hover:scale-105'
                } text-white rounded-full p-2 sm:p-3 focus:outline-none focus:ring-2 focus:ring-blue-400 transition-all duration-300 ease-in-out`}
              >
                {isLoading ? (
                  <Loader2 className="w-5 h-5 sm:w-6 sm:h-6 animate-spin" />
                ) : (
                  <Send className="w-5 h-5 sm:w-6 sm:h-6" />
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 