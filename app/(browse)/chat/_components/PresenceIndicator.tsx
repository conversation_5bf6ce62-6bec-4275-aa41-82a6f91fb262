import React from 'react'
import { motion } from 'framer-motion'
import type { PresenceStatus } from '@/services/COMMUNICATION/interfaces/channel'

interface PresenceIndicatorProps {
  status: PresenceStatus
  size?: 'sm' | 'md' | 'lg'
  showPulse?: boolean
  className?: string
}

const statusConfig = {
  online: {
    color: 'bg-green-500',
    borderColor: 'border-green-400',
    label: 'Online'
  },
  away: {
    color: 'bg-yellow-500',
    borderColor: 'border-yellow-400',
    label: 'Ausente'
  },
  offline: {
    color: 'bg-gray-500',
    borderColor: 'border-gray-400',
    label: 'Offline'
  }
}

const sizeConfig = {
  sm: 'w-2 h-2',
  md: 'w-3 h-3',
  lg: 'w-4 h-4'
}

export function PresenceIndicator({ 
  status, 
  size = 'sm', 
  showPulse = true,
  className = '' 
}: PresenceIndicatorProps) {
  const config = statusConfig[status]
  const sizeClass = sizeConfig[size]

  return (
    <div className={`relative ${className}`} title={config.label}>
      <div 
        className={`
          ${sizeClass} 
          ${config.color} 
          rounded-full 
          border-2 
          ${config.borderColor}
          relative
          z-10
        `}
      />
      {showPulse && status === 'online' && (
        <motion.div
          className={`
            absolute 
            inset-0 
            ${config.color} 
            rounded-full 
            opacity-75
          `}
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.75, 0, 0.75]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      )}
    </div>
  )
}

interface PresenceListProps {
  users: Array<{ id: string; status: PresenceStatus; name?: string }>
  maxVisible?: number
  className?: string
}

export function PresenceList({ users, maxVisible = 5, className = '' }: PresenceListProps) {
  const onlineUsers = users.filter(u => u.status === 'online')
  const awayUsers = users.filter(u => u.status === 'away')
  const visibleUsers = [...onlineUsers, ...awayUsers].slice(0, maxVisible)
  const remainingCount = users.length - visibleUsers.length

  if (users.length === 0) return null

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      {visibleUsers.map((user, index) => (
        <div key={user.id} className="relative">
          <PresenceIndicator 
            status={user.status} 
            size="sm"
            showPulse={user.status === 'online'}
          />
        </div>
      ))}
      {remainingCount > 0 && (
        <span className="text-xs text-blue-300 ml-1">
          +{remainingCount}
        </span>
      )}
    </div>
  )
}

export default PresenceIndicator
