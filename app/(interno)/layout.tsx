"use client"

import React, { useEffect, ReactNode, Suspense } from 'react'
import { useAuth } from '@/context/AuthContext'
import Header from '@/components/header/Header'
import PageTransition from '@/components/PageTransition'
import Loading from '@/components/Loading'
import { useRouter, usePathname } from 'next/navigation'

export default function InternoLayout({ children }: { children: ReactNode }) {
  const { authData, isLoading } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    if (!isLoading) {
      if (!authData) {
        router.push('/login')
        return
      }

      // Check if user has admin role
      if (authData.role !== 'admin') {
        router.push('/inicio')
      }
    }
  }, [authData, router, pathname, isLoading])

  if (isLoading) return <Loading />

  if (!isLoading && !authData) return null

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-1">
        <Suspense fallback={<Loading />}>
          <PageTransition>
            {children}
          </PageTransition>
        </Suspense>
      </main>
    </div>
  )
}
