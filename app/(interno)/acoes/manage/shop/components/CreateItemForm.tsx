"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { IItem } from "@/services/SHOP/interfaces/item";
import {
  ICreateItemData,
  ICreateItemsRequest,
} from "@/services/SHOP/interfaces/itemsApi";
import ShopAPI from "@/services/SHOP/api";

const ITEM_CATEGORIES = [
  { value: "GiftCard", label: "Gift Card" },
  { value: "clothing", label: "Vestuário" },
  { value: "accessories", label: "Acessórios" },
  { value: "electronics", label: "Eletrônicos" },
  { value: "books", label: "Livros" },
  { value: "other", label: "Outros" },
];

const ITEM_TYPES = [
  { value: "GiftCard", label: "Gift Card" },
  { value: "AtomizeMerch", label: "Atomize Merch" },
  { value: "DigitalProduct", label: "Produto Digital" },
  { value: "PhysicalProduct", label: "Produto Físico" },
];

const USAGE_TYPES = [
  { value: "Redeemable", label: "Resgatável" },
  { value: "Retrievable", label: "Recuperável" },
  { value: "Consumable", label: "Consumível" },
  { value: "Permanent", label: "Permanente" },
];

export default function CreateItemForm() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    category: "",
    type: "",
    usageType: "",
    basePrice: "",
    finalPrice: "",
    stock: "",
    sku: "",
    isAutomatic: false,
    tags: [] as string[],
    images: [] as string[],
  });

  const handleInputChange = (
    field: string,
    value: string | boolean | string[]
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const basePrice = parseFloat(formData.basePrice)
      const finalPrice = parseFloat(formData.finalPrice)
      
      // Validate that final price is not greater than base price
      if (finalPrice > basePrice) {
        toast({
          title: "Erro",
          description: "O preço final não pode ser maior que o preço base.",
          variant: "destructive",
        });
        return;
      }

      const itemData: ICreateItemData = {
        name: formData.name,
        description: formData.description,
        category: formData.category,
        type: formData.type,
        usageType: formData.usageType,
        tags: formData.tags,
        currentPrice: finalPrice,
        basePrice: basePrice,
        stockQuantity: parseInt(formData.stock),
        sku: formData.sku,
        isAutomatic: formData.isAutomatic,
      };

      const createRequest: ICreateItemsRequest = {
        itemsData: [itemData],
      };

      // Call the SHOP API to create the item
      const response = await ShopAPI.createItems(createRequest);

      toast({
        title: "Sucesso!",
        description: "Item criado com sucesso na loja global.",
        variant: "default",
      });

             // Reset form
       setFormData({
         name: "",
         description: "",
         category: "",
         type: "",
         usageType: "",
         basePrice: "",
         finalPrice: "",
         stock: "",
         sku: "",
         isAutomatic: false,
         tags: [],
         images: [],
       });
    } catch (error) {
      console.error("Error creating item:", error);
      toast({
        title: "Erro",
        description: "Erro ao criar item. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="p-6 bg-blue-950/10 border-blue-500/20">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white mb-4">
              Informações Básicas
            </h3>

            <div className="space-y-2">
              <Label htmlFor="name" className="text-blue-300">
                Nome do Item *
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Nome do produto"
                required
                className="bg-blue-950/30 border-blue-500/30 text-white"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-blue-300">
                Descrição *
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  handleInputChange("description", e.target.value)
                }
                placeholder="Descrição detalhada do produto"
                required
                className="bg-blue-950/30 border-blue-500/30 text-white"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category" className="text-blue-300">
                Categoria *
              </Label>
              <Select
                value={formData.category}
                onValueChange={(value) => handleInputChange("category", value)}
              >
                <SelectTrigger className="bg-blue-950/30 border-blue-500/30 text-white">
                  <SelectValue placeholder="Selecione uma categoria" />
                </SelectTrigger>
                <SelectContent className="bg-blue-950 border-blue-500">
                  {ITEM_CATEGORIES.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="type" className="text-blue-300">
                Tipo *
              </Label>
              <Select
                value={formData.type}
                onValueChange={(value) => handleInputChange("type", value)}
              >
                <SelectTrigger className="bg-blue-950/30 border-blue-500/30 text-white">
                  <SelectValue placeholder="Selecione um tipo" />
                </SelectTrigger>
                <SelectContent className="bg-blue-950 border-blue-500">
                  {ITEM_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="usageType" className="text-blue-300">
                Tipo de Uso *
              </Label>
              <Select
                value={formData.usageType}
                onValueChange={(value) => handleInputChange("usageType", value)}
              >
                <SelectTrigger className="bg-blue-950/30 border-blue-500/30 text-white">
                  <SelectValue placeholder="Selecione o tipo de uso" />
                </SelectTrigger>
                <SelectContent className="bg-blue-950 border-blue-500">
                  {USAGE_TYPES.map((usageType) => (
                    <SelectItem key={usageType.value} value={usageType.value}>
                      {usageType.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tags" className="text-blue-300">
                Tags
              </Label>
              <Input
                id="tags"
                value={formData.tags.join(", ")}
                onChange={(e) =>
                  handleInputChange(
                    "tags",
                    e.target.value
                      .split(",")
                      .map((tag) => tag.trim())
                      .filter((tag) => tag)
                  )
                }
                placeholder="roblox, gaming, digital (separados por vírgula)"
                className="bg-blue-950/30 border-blue-500/30 text-white"
              />
            </div>
          </div>

          {/* Pricing and Stock */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white mb-4">
              Preços e Estoque
            </h3>

            <div className="space-y-2">
              <Label htmlFor="basePrice" className="text-blue-300">
                Preço Base *
              </Label>
              <Input
                id="basePrice"
                type="number"
                step="0.01"
                value={formData.basePrice}
                onChange={(e) => handleInputChange("basePrice", e.target.value)}
                placeholder="0.00"
                required
                className="bg-blue-950/30 border-blue-500/30 text-white"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="finalPrice" className="text-blue-300">
                Preço Final *
              </Label>
              <Input
                id="finalPrice"
                type="number"
                step="0.01"
                value={formData.finalPrice}
                onChange={(e) => handleInputChange("finalPrice", e.target.value)}
                placeholder="0.00"
                required
                className="bg-blue-950/30 border-blue-500/30 text-white"
              />
            </div>

            {/* Calculated Discount Display */}
            {formData.basePrice && formData.finalPrice && (
              <div className="space-y-2">
                <Label className="text-blue-300">Desconto Calculado</Label>
                <div className="p-3 bg-red-900/20 border border-red-500/30 rounded-lg">
                  <span className="text-red-400 font-semibold">
                    {(() => {
                      const basePrice = parseFloat(formData.basePrice) || 0
                      const finalPrice = parseFloat(formData.finalPrice) || 0
                      const discount = basePrice - finalPrice
                      return `${discount.toFixed(2)} ATOMs`
                    })()}
                  </span>
                  {parseFloat(formData.finalPrice) < parseFloat(formData.basePrice) && (
                    <span className="text-red-300 text-sm ml-2">
                      (Desconto de {((parseFloat(formData.basePrice) - parseFloat(formData.finalPrice)) / parseFloat(formData.basePrice) * 100).toFixed(2)}%)
                    </span>
                  )}
                </div>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="stock" className="text-blue-300">
                Estoque *
              </Label>
              <Input
                id="stock"
                type="number"
                min="0"
                value={formData.stock}
                onChange={(e) => handleInputChange("stock", e.target.value)}
                placeholder="0"
                required
                className="bg-blue-950/30 border-blue-500/30 text-white"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="sku" className="text-blue-300">
                SKU *
              </Label>
              <Input
                id="sku"
                value={formData.sku}
                onChange={(e) => handleInputChange("sku", e.target.value)}
                placeholder="Código SKU"
                required
                className="bg-blue-950/30 border-blue-500/30 text-white"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="isAutomatic"
                checked={formData.isAutomatic}
                onCheckedChange={(checked: boolean) =>
                  handleInputChange("isAutomatic", checked)
                }
              />
              <Label htmlFor="isAutomatic" className="text-blue-300">
                Processamento Automático
              </Label>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-4 pt-6 border-t border-blue-500/30">
          <Button
            type="button"
            variant="outline"
                         onClick={() => {
               setFormData({
                 name: "",
                 description: "",
                 category: "",
                 type: "",
                 usageType: "",
                 basePrice: "",
                 finalPrice: "",
                 stock: "",
                 sku: "",
                 isAutomatic: false,
                 tags: [],
                 images: [],
               });
             }}
            className="border-blue-500/30 text-blue-300 hover:bg-blue-950/30"
          >
            Limpar
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            {isLoading ? "Criando..." : "Criar Item"}
          </Button>
        </div>
      </form>
    </Card>
  );
}
