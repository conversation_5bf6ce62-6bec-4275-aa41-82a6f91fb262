"use client"

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { IInventoryItem } from "@/services/SHOP/interfaces/inventoryItem"
import ShopAPI from "@/services/SHOP/api"
import { GiftIcon, Loader2 } from 'lucide-react'

interface UpdateGiftCardModalProps {
  isOpen: boolean
  onClose: () => void
  order: IInventoryItem | null
  onUpdate: () => void
}

export default function UpdateGiftCardModal({ isOpen, onClose, order, onUpdate }: UpdateGiftCardModalProps) {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    redeemCode: '',
    redeemUrl: ''
  })

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!order) return

    setIsLoading(true)
    try {
      // Use the admin moveInventoryItem endpoint to update metadata
      const moveRequest = {
        targetOwnerId: order.ownerId, // Required for admin endpoint
        itemId: order.itemId._id || order.itemId.id || '', // Use the item ID from the nested itemId object
        targetStatus: 'Ready', // Change from Pending to Ready
        order: order.order,
        metadata: {
          redeemCode: formData.redeemCode,
          redeemUrl: formData.redeemUrl
        }
      }

      console.log('Move request payload:', moveRequest)

      await ShopAPI.inventoryController.adminMoveInventoryItem(moveRequest)
      
      toast({
        title: "Sucesso!",
        description: "Metadados do gift card atualizados com sucesso.",
        variant: "default"
      })

      onUpdate()
      onClose()
      setFormData({ redeemCode: '', redeemUrl: '' })
    } catch (error) {
      console.error('Error updating gift card metadata:', error)
      toast({
        title: "Erro",
        description: "Erro ao atualizar metadados do gift card.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    setFormData({ redeemCode: '', redeemUrl: '' })
    onClose()
  }

  if (!order) return null

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="bg-blue-950/80 backdrop-blur-md border-blue-500/30 text-blue-100 max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500 flex items-center gap-2">
            <GiftIcon className="h-6 w-6 text-blue-400" />
            Atualizar Gift Card
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label className="text-blue-300 text-sm">Item</Label>
            <div className="bg-blue-900/30 border border-blue-500/30 rounded-md p-3">
              <p className="text-white font-medium">{order.itemId.name}</p>
              <p className="text-blue-300 text-sm">{order.itemId.description}</p>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="redeemCode" className="text-blue-300 text-sm">
              Código de Resgate *
            </Label>
            <Input
              id="redeemCode"
              value={formData.redeemCode}
              onChange={(e) => handleInputChange('redeemCode', e.target.value)}
              placeholder="Digite o código de resgate"
              required
              className="bg-blue-950/30 border-blue-500/30 text-white"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="redeemUrl" className="text-blue-300 text-sm">
              URL de Resgate
            </Label>
            <Input
              id="redeemUrl"
              value={formData.redeemUrl}
              onChange={(e) => handleInputChange('redeemUrl', e.target.value)}
              placeholder="https://example.com/redeem"
              type="url"
              className="bg-blue-950/30 border-blue-500/30 text-white"
            />
          </div>

          <DialogFooter className="flex gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
              className="border-blue-500/30 text-blue-300 hover:bg-blue-950/30"
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Atualizando...
                </>
              ) : (
                'Atualizar'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
