"use client"

import { useState, useEffect } from 'react'
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { IItem, IGetItemsByFilterResponse } from "@/services/SHOP/interfaces"
import { IAddItemsToShopRequest } from "@/services/SHOP/interfaces/shop"
import ShopAPI from "@/services/SHOP/api"
import { Search, Globe, Building, Loader2, Plus } from 'lucide-react'

export default function ShopAssignment() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingItems, setIsLoadingItems] = useState(false)
  const [items, setItems] = useState<IItem[]>([])
  const [filteredItems, setFilteredItems] = useState<IItem[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [selectedShop, setSelectedShop] = useState<'GLOBAL' | 'LOCAL'>('GLOBAL')
  const [userContractShopId, setUserContractShopId] = useState<string | null>(null)

  // Load user's contract shop ID
  useEffect(() => {
    const loadUserContractShop = async () => {
      try {
        // TODO: Replace with actual API call to get user's contract shop ID
        // For now, using a placeholder - you'll need to implement this
        const userInfo = localStorage.getItem('user')
        if (userInfo) {
          const user = JSON.parse(userInfo)
          // Assuming the user object has contract information
          // You might need to call an API like: await ShopAPI.getUserContractShop()
          setUserContractShopId(user.contractShopId || null)
        }
      } catch (error) {
        console.error('Error loading user contract shop:', error)
      }
    }

    loadUserContractShop()
  }, [])

  // Load all available items
  useEffect(() => {
    const loadItems = async () => {
      setIsLoadingItems(true)
      try {
        const response: IGetItemsByFilterResponse = await ShopAPI.getItemsByFilter()
        console.log('Loaded items:', response.items)
        // Check the first item to see what ID field is available
        if (response.items.length > 0) {
          const firstItem = response.items[0]
          console.log('First item structure:', firstItem)
          console.log('ID fields:', {
            _id: firstItem._id,
            id: firstItem.id
          })
        }
        setItems(response.items)
        setFilteredItems(response.items)
      } catch (error) {
        console.error('Error loading items:', error)
        toast({
          title: "Erro",
          description: "Erro ao carregar itens disponíveis.",
          variant: "destructive"
        })
      } finally {
        setIsLoadingItems(false)
      }
    }

    loadItems()
  }, [toast])

  // Filter items based on search
  useEffect(() => {
    const filtered = items.filter(item =>
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.category.toLowerCase().includes(searchTerm.toLowerCase())
    )
    setFilteredItems(filtered)
  }, [items, searchTerm])

  // Debug selected items changes
  useEffect(() => {
    console.log('Selected items changed:', selectedItems)
  }, [selectedItems])

  // Helper function to get the correct ID from an item
  const getItemId = (item: IItem): string => {
    return item._id || item.id || ''
  }

  const handleItemToggle = (itemId: string) => {
    console.log('Toggling item:', itemId, 'Current selected:', selectedItems)
    setSelectedItems(prev => {
      const newSelection = prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
      console.log('New selection:', newSelection)
      return newSelection
    })
  }

  const handleAssignToShop = async () => {
    if (selectedItems.length === 0) {
      toast({
        title: "Aviso",
        description: "Selecione pelo menos um item para atribuir à loja.",
        variant: "destructive"
      })
      return
    }

    setIsLoading(true)
    try {
                    // Get the selected items
       const itemsToAssign = items.filter(item => selectedItems.includes(getItemId(item)))
       
                // Prepare the request
         const shopItems = itemsToAssign.map((item, index) => ({
           item: {
             id: getItemId(item),
           name: item.name,
           description: item.description,
           basePrice: item.basePrice,
           currentPrice: item.currentPrice,
           discount: item.discount,
           stock: item.stock,
           category: item.category,
           type: item.type,
           usageType: item.usageType,
           tags: item.tags,
           images: item.images,
           createdAt: item.createdAt,
           updatedAt: item.updatedAt,
                        _id: getItemId(item),
           isAutomatic: item.isAutomatic
         },
         order: index + 1
       }))

      // Determine shop ID based on selection
      const shopId = selectedShop === 'GLOBAL' ? null : userContractShopId
      
      if (selectedShop === 'LOCAL' && !userContractShopId) {
        toast({
          title: "Erro",
          description: "Não foi possível obter o ID da loja do seu contrato.",
          variant: "destructive"
        })
        return
      }

      const request: IAddItemsToShopRequest = {
        shopId: shopId,
        itemIds: selectedItems
      }
      
      // Add items to the selected shop
      await ShopAPI.addItemsToShop(request)
      
      toast({
        title: "Sucesso!",
        description: `${selectedItems.length} item(s) atribuído(s) à loja ${selectedShop === 'GLOBAL' ? 'global' : 'local'}.`,
        variant: "default"
      })

      // Clear selection
      setSelectedItems([])
      
    } catch (error) {
      console.error('Error assigning items to shop:', error)
      toast({
        title: "Erro",
        description: "Erro ao atribuir itens à loja. Tente novamente.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="p-6 bg-blue-950/10 border-blue-500/20">
      <div className="space-y-6">
        {/* Header */}
        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-white">Atribuir Itens às Lojas</h3>
          <p className="text-blue-300 text-sm">
            Selecione itens existentes e atribua-os à loja global ou à loja local do seu contrato.
          </p>
        </div>

        {/* Shop Selection */}
        <div className="space-y-2">
          <Label className="text-blue-300">Loja de Destino</Label>
          <Select value={selectedShop} onValueChange={(value: 'GLOBAL' | 'LOCAL') => setSelectedShop(value)}>
            <SelectTrigger className="bg-blue-950/30 border-blue-500/30 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-blue-950 border-blue-500">
              <SelectItem value="GLOBAL">
                <div className="flex items-center gap-2">
                  <Globe className="w-4 h-4" />
                  Loja Global
                </div>
              </SelectItem>
              <SelectItem value="LOCAL">
                <div className="flex items-center gap-2">
                  <Building className="w-4 h-4" />
                  Loja Local (Seu Contrato)
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
          {selectedShop === 'LOCAL' && !userContractShopId && (
            <p className="text-orange-400 text-sm">
              ⚠️ Não foi possível carregar o ID da loja do seu contrato. Entre em contato com o suporte.
            </p>
          )}
        </div>

        {/* Search */}
        <div className="space-y-2">
          <Label className="text-blue-300">Buscar Itens</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-400 w-4 h-4" />
            <Input
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Buscar por nome, descrição ou categoria..."
              className="pl-10 bg-blue-950/30 border-blue-500/30 text-white"
            />
          </div>
        </div>

        {/* Items List */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-blue-300">Itens Disponíveis</Label>
            <span className="text-blue-400 text-sm">
              {selectedItems.length} de {filteredItems.length} selecionado(s)
            </span>
          </div>
          
          {isLoadingItems ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="animate-spin h-6 w-6 text-blue-400" />
              <span className="ml-2 text-blue-300">Carregando itens...</span>
            </div>
          ) : (
            <div className="max-h-96 overflow-y-auto space-y-2 border border-blue-500/30 rounded-lg p-4 bg-blue-950/20">
              {filteredItems.length === 0 ? (
                <p className="text-blue-400 text-center py-4">Nenhum item encontrado</p>
              ) : (
                                 filteredItems.map((item) => (
                   <div
                     key={getItemId(item)}
                     className={`flex items-center justify-between p-3 rounded-lg border transition-colors cursor-pointer ${
                       selectedItems.includes(getItemId(item))
                         ? 'bg-blue-600/30 border-blue-500/50'
                         : 'bg-blue-950/20 border-blue-500/20 hover:bg-blue-950/40'
                     }`}
                     onClick={() => {
                       console.log('Clicking item:', getItemId(item), 'Item data:', item)
                       handleItemToggle(getItemId(item))
                     }}
                   >
                    <div className="flex-1">
                      <h4 className="font-medium text-white">{item.name}</h4>
                      <p className="text-blue-300 text-sm">{item.description}</p>
                      <div className="flex items-center gap-4 mt-1">
                        <span className="text-blue-400 text-xs">{item.category}</span>
                        <span className="text-blue-400 text-xs">{item.type}</span>
                        <span className="text-green-400 text-xs">{item.currentPrice} ATOMs</span>
                      </div>
                    </div>
                                         <div className="flex items-center gap-2">
                       {selectedItems.includes(getItemId(item)) && (
                         <Plus className="w-4 h-4 text-green-400" />
                       )}
                       <span className="text-xs text-blue-400">ID: {getItemId(item)}</span>
                     </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-blue-500/30">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              setSelectedItems([])
              setSearchTerm('')
            }}
            className="border-blue-500/30 text-blue-300 hover:bg-blue-950/30"
          >
            Limpar Seleção
          </Button>
          <Button
            onClick={handleAssignToShop}
            disabled={isLoading || selectedItems.length === 0 || (selectedShop === 'LOCAL' && !userContractShopId)}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Atribuindo...
              </>
            ) : (
              <>
                <Plus className="mr-2 h-4 w-4" />
                Atribuir à Loja
              </>
            )}
          </Button>
        </div>
      </div>
    </Card>
  )
}
