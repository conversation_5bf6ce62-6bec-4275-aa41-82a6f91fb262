"use client"

import { useState } from 'react'
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { PlusIcon, ClockIcon, StoreIcon } from 'lucide-react'
import CreateItemForm from './components/CreateItemForm'
import PendingOrders from './components/PendingOrders'
import ShopAssignment from './components/ShopAssignment'

export default function ShopManagementPage() {
  return (
    <div className="container mx-auto p-3 sm:p-4 lg:p-6">
      <div className="space-y-2 mb-8">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-green-200 via-blue-200 to-green-200 text-transparent bg-clip-text">
          Gerenciamento da Loja
        </h1>
        <p className="text-green-400 mt-1">
          Gerencie itens da loja global e pedidos pendentes
        </p>
      </div>

      <Card className="p-6 bg-blue-950/20 border-blue-500/30">
        <Tabs defaultValue="create" className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-blue-950/50">
            <TabsTrigger 
              value="create" 
              className="flex items-center gap-2 data-[state=active]:bg-green-600 data-[state=active]:text-white"
            >
              <PlusIcon className="w-4 h-4" />
              Criar Item
            </TabsTrigger>
            <TabsTrigger 
              value="assign" 
              className="flex items-center gap-2 data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              <StoreIcon className="w-4 h-4" />
              Atribuir às Lojas
            </TabsTrigger>
            <TabsTrigger 
              value="pending" 
              className="flex items-center gap-2 data-[state=active]:bg-orange-600 data-[state=active]:text-white"
            >
              <ClockIcon className="w-4 h-4" />
              Pedidos Pendentes
            </TabsTrigger>
          </TabsList>

          <TabsContent value="create" className="mt-6">
            <CreateItemForm />
          </TabsContent>

          <TabsContent value="assign" className="mt-6">
            <ShopAssignment />
          </TabsContent>

          <TabsContent value="pending" className="mt-6">
            <PendingOrders />
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  )
}
